#!/usr/bin/env python3
"""
Test script for the enhanced VideoFileSource class.
Demonstrates live camera-like behavior and production features.
"""

import time
import logging
from showcase_framework import VideoFileSource

# Configure logging
logging.basicConfig(level=logging.INFO, format="[%(levelname)s] %(message)s")
logger = logging.getLogger(__name__)

def test_basic_functionality():
    """Test basic video reading functionality."""
    print("\n=== Testing Basic Functionality ===")
    
    video_path = "/home/<USER>/jk/dev/hailotest/videos/people1_30s.mp4"
    
    try:
        with VideoFileSource(video_path) as source:
            print(f"Video FPS: {source.fps}")
            print(f"Frame count: {source.frame_count}")
            print(f"Duration: {source.get_duration():.2f}s")
            
            # Read a few frames
            for i in range(5):
                frame = source.read()
                if frame is not None:
                    print(f"Frame {i}: shape={frame.shape}, dtype={frame.dtype}")
                else:
                    print(f"Frame {i}: None (end of video)")
                    break
                    
    except Exception as e:
        print(f"Error: {e}")

def test_realtime_behavior():
    """Test real-time frame pacing (live camera emulation)."""
    print("\n=== Testing Real-Time Behavior ===")
    
    video_path = "/home/<USER>/jk/dev/hailotest/videos/people1_30s.mp4"
    
    try:
        # Create source with real-time pacing
        source = VideoFileSource(
            path=video_path,
            realtime=True,
            speed=2.0,  # 2x speed for faster testing
            buffer_size=3
        )
        
        source.start()
        start_time = time.time()
        frame_count = 0
        
        print("Reading frames with real-time pacing (2x speed)...")
        
        while frame_count < 10:  # Read 10 frames
            frame = source.read()
            if frame is None:
                break
                
            frame_count += 1
            elapsed = time.time() - start_time
            
            # Get statistics
            stats = source.get_stats()
            print(f"Frame {frame_count}: elapsed={elapsed:.2f}s, "
                  f"effective_fps={stats['effective_fps']:.1f}, "
                  f"progress={stats['progress']:.1%}")
            
        source.stop()
        
    except Exception as e:
        print(f"Error: {e}")

def test_loop_playback():
    """Test continuous loop playback."""
    print("\n=== Testing Loop Playback ===")
    
    video_path = "/home/<USER>/jk/dev/hailotest/videos/people1_30s.mp4"
    
    try:
        source = VideoFileSource(
            path=video_path,
            realtime=True,
            loop=True,
            speed=5.0  # 5x speed for faster testing
        )
        
        source.start()
        start_time = time.time()
        frame_count = 0
        
        print("Testing loop playback (5x speed, will loop automatically)...")
        
        while frame_count < 20:  # Read more frames than video has
            frame = source.read()
            if frame is None:
                print("Unexpected end of frames")
                break
                
            frame_count += 1
            stats = source.get_stats()
            
            if frame_count % 5 == 0:  # Print every 5th frame
                print(f"Frame {frame_count}: progress={stats['progress']:.1%}, "
                      f"current_frame={stats['current_frame']}")
            
        source.stop()
        
    except Exception as e:
        print(f"Error: {e}")

def test_seeking_and_control():
    """Test seeking and playback control features."""
    print("\n=== Testing Seeking and Control ===")
    
    video_path = "/home/<USER>/jk/dev/hailotest/videos/people1_30s.mp4"
    
    try:
        source = VideoFileSource(video_path)
        source.start()
        
        # Test seeking to different positions
        positions = [0, 0.25, 0.5, 0.75, 1.0]  # 0%, 25%, 50%, 75%, 100%
        
        for pos in positions:
            if pos == 1.0:
                # Seek to near end
                if source.frame_count:
                    target_frame = max(0, source.frame_count - 5)
                    source.seek(target_frame)
            else:
                # Seek by time
                duration = source.get_duration()
                if duration:
                    target_time = duration * pos
                    source.seek_time(target_time)
            
            # Read a frame at this position
            frame = source.read()
            stats = source.get_stats()
            
            print(f"Seek to {pos:.0%}: frame={stats['current_frame']}, "
                  f"progress={stats['progress']:.1%}")
        
        # Test speed changes
        print("\nTesting speed changes:")
        speeds = [0.5, 1.0, 2.0, 0.25]
        source.reset()  # Go back to start
        
        for speed in speeds:
            source.set_speed(speed)
            print(f"Speed set to {speed}x")
            
            # Read a few frames at this speed
            for _ in range(3):
                frame = source.read()
                if frame is None:
                    break
                time.sleep(0.1)  # Small delay to see timing effect
        
        source.stop()
        
    except Exception as e:
        print(f"Error: {e}")

def test_error_handling():
    """Test error handling and recovery."""
    print("\n=== Testing Error Handling ===")
    
    # Test with non-existent file
    try:
        source = VideoFileSource("nonexistent_video.mp4", max_retries=2)
        source.start()
        print("ERROR: Should have failed with non-existent file")
    except Exception as e:
        print(f"✓ Correctly handled non-existent file: {e}")
    
    # Test with invalid parameters
    try:
        source = VideoFileSource("", speed=-1.0)
        print("ERROR: Should have failed with invalid parameters")
    except Exception as e:
        print(f"✓ Correctly handled invalid parameters: {e}")

def test_statistics_monitoring():
    """Test statistics and monitoring features."""
    print("\n=== Testing Statistics and Monitoring ===")
    
    video_path = "/home/<USER>/jk/dev/hailotest/videos/people1_30s.mp4"
    
    try:
        source = VideoFileSource(
            path=video_path,
            realtime=True,
            buffer_size=5
        )
        
        source.start()
        
        # Read frames and monitor statistics
        for i in range(10):
            frame = source.read()
            if frame is None:
                break
                
            stats = source.get_stats()
            
            if i % 3 == 0:  # Print every 3rd frame
                print(f"Stats - Frames read: {stats['frames_read']}, "
                      f"Errors: {stats['error_count']}, "
                      f"Buffer size: {stats['buffer_size']}, "
                      f"Effective FPS: {stats['effective_fps']:.1f}")
        
        # Final statistics
        final_stats = source.get_stats()
        print(f"\nFinal Statistics:")
        for key, value in final_stats.items():
            print(f"  {key}: {value}")
        
        source.stop()
        
    except Exception as e:
        print(f"Error: {e}")

def main():
    """Run all tests."""
    print("Enhanced VideoFileSource Test Suite")
    print("=" * 50)
    
    test_basic_functionality()
    test_realtime_behavior()
    test_loop_playback()
    test_seeking_and_control()
    test_error_handling()
    test_statistics_monitoring()
    
    print("\n" + "=" * 50)
    print("Test suite completed!")

if __name__ == "__main__":
    main()
