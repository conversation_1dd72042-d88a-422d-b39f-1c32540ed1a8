#!/usr/bin/env python3
"""
hailo_console_profiler.py
Producer/consumer FPS + detection profiler for DeGirum + Hailo on Raspberry Pi.

Usage:
    python hailo_console_profiler.py --width 640 --height 480
    python hailo_console_profiler.py --match-model-input
"""
import os
import time
import argparse
import logging
import threading
import queue
import numpy as np

import degirum as dg
from picamera2 import Picamera2

logging.basicConfig(level=logging.INFO, format='[%(levelname)s] %(message)s')

class RetinaFaceDetector:
    def __init__(self, model_name: str, zoo_path: str, fast_resize=False, colorspace='RGB'):
        logging.info("Connecting to DeGirum in local mode—zoo: %s", zoo_path)
        self.zoo = dg.connect(dg.LOCAL, zoo_path)
        logging.info("Loading model: %s", model_name)
        self.model = self.zoo.load_model(model_name)
        logging.info("Model loaded successfully.")

        # Configure model properties for performance & correctness
        # collect device-side timing
        try:
            self.model.measure_time = True
        except Exception:
            pass

        # Tell the model which colorspace our numpy frames will be (RGB or BGR)
        try:
            self.model.input_numpy_colorspace = colorspace  # 'RGB' or 'BGR'
        except Exception:
            pass

        # Resize/pad method: 'stretch' is fastest (no letterbox padding), 'letterbox' preserves aspect.
        # Resize algorithm: 'area' or 'nearest' are generally faster than bicubic/lanczos.
        try:
            if fast_resize:
                self.model.input_pad_method = "stretch"
                self.model.input_resize_method = "nearest"
            else:
                self.model.input_pad_method = "letterbox"
                self.model.input_resize_method = "area"
        except Exception:
            pass

        # Optional tuning: how many in-flight frames allowed by the model
        try:
            self.model.frame_queue_depth = 4
        except Exception:
            pass

    def preprocess(self, frame_rgb):
        # We will pass RGB arrays (so avoid extra conversions)
        # DeGirum expects either RGB or BGR depending on input_numpy_colorspace
        # Cast to uint8 numpy array (if needed)
        if frame_rgb.dtype != np.uint8:
            frame_rgb = (frame_rgb * 255).astype(np.uint8)
        return frame_rgb

    def infer(self, frame_rgb):
        # single-frame predict (blocking)
        return self.model.predict(frame_rgb)

    def num_detections(self, outputs):
        try:
            return len(outputs.results)
        except Exception:
            return 0

def capture_thread_fn(pic, q: queue.Queue, stop_event: threading.Event):
    while not stop_event.is_set():
        try:
            frame = pic.capture_array()  # format set by user (we will request RGB888)
            # frame is numpy array (H,W,3)
            try:
                q.put(frame, timeout=0.5)
            except queue.Full:
                # drop frame if queue is full to avoid blocking the capture thread
                continue
        except Exception as e:
            logging.error("Capture error: %s", e)
            break

def inference_thread_fn(detector: RetinaFaceDetector, q: queue.Queue, stop_event: threading.Event,
                        stats: dict):
    frame_count = 0
    detections_acc = 0
    per_sec_start = time.time()

    # per-stage accumulators (for simple averages)
    acc_capture = acc_pre = acc_infer = 0.0
    cycles = 0

    while not stop_event.is_set():
        try:
            frame = q.get(timeout=0.5)
        except queue.Empty:
            continue

        t0 = time.time()
        # NOTE: capture time accounted implicitly - capture thread runs concurrently
        t_pre0 = time.time()
        # we assume the camera already gives RGB arrays (RGB888). If camera is BGR, convert here.
        input_frame = detector.preprocess(frame)
        t_pre1 = time.time()

        outputs = detector.infer(input_frame)
        t_inf1 = time.time()

        n = detector.num_detections(outputs)
        detections_acc += n
        frame_count += 1

        # accumulate times
        acc_pre += (t_pre1 - t_pre0)
        acc_infer += (t_inf1 - t_pre1)
        cycles += 1

        elapsed = time.time() - per_sec_start
        if elapsed >= 1.0:
            fps = frame_count / elapsed
            stats['fps'] = fps
            stats['detections_per_sec'] = detections_acc
            stats['avg_pre'] = acc_pre / cycles if cycles else 0.0
            stats['avg_infer'] = acc_infer / cycles if cycles else 0.0
            # reset counters
            frame_count = 0
            detections_acc = 0
            acc_pre = acc_infer = 0.0
            cycles = 0
            per_sec_start = time.time()

def main():
    parser = argparse.ArgumentParser(description="Hailo + DeGirum profiler with threading")
    parser.add_argument("--width", type=int, default=640, help="camera width (default 640)")
    parser.add_argument("--height", type=int, default=480, help="camera height (default 480)")
    parser.add_argument("--match-model-input", action="store_true",
                        help="Try to set the camera resolution to the model input shape")
    parser.add_argument("--fast-resize", action="store_true",
                        help="Use faster (but lower quality) resize/stretch inside PySDK")
    parser.add_argument("--queue-depth", type=int, default=2, help="Frame queue depth")
    parser.add_argument("--zoo", default="~/degirum-zoo", help="Degirum zoo path")
    parser.add_argument("--model", default="retinaface_mobilenet--736x1280_quant_hailort_hailo8l_1",
                        help="Model name in Zoo")
    parser.add_argument("--colorspace", default="RGB", choices=["RGB", "BGR"],
                        help="What colorspace to send to the model (default RGB).")
    args = parser.parse_args()

    zoo_path = os.path.expanduser(args.zoo)
    detector = RetinaFaceDetector(args.model, zoo_path,
                                  fast_resize=args.fast_resize,
                                  colorspace=args.colorspace)

    # detect model input shape if possible
    try:
        ms = detector.model.input_shape  # list like [[N,H,W,C]]
        if isinstance(ms, list) and len(ms) and isinstance(ms[0], (list, tuple)) and len(ms[0]) >= 4:
            _, model_h, model_w, _ = ms[0]
            logging.info("Model input shape detected: %s", ms[0])
        else:
            model_w, model_h = args.width, args.height
    except Exception:
        model_w, model_h = args.width, args.height

    # if user asked to match model input, override width/height
    cam_w = args.width
    cam_h = args.height
    if args.match_model_input:
        cam_w, cam_h = model_w, model_h
        logging.info("Matching camera resolution to model input: %dx%d", cam_w, cam_h)
    else:
        logging.info("Camera resolution: %dx%d (model: %dx%d)", cam_w, cam_h, model_w, model_h)

    picam2 = Picamera2()
    preview_cfg = picam2.create_preview_configuration(
        main={"format": "RGB888", "size": (cam_w, cam_h)}
    )
    picam2.configure(preview_cfg)
    picam2.start()
    logging.info("Camera started")

    frame_q = queue.Queue(maxsize=max(1, args.queue_depth))
    stop_event = threading.Event()
    stats = {'fps': 0.0, 'detections_per_sec': 0, 'avg_pre': 0.0, 'avg_infer': 0.0}

    cap_t = threading.Thread(target=capture_thread_fn, args=(picam2, frame_q, stop_event), daemon=True)
    inf_t = threading.Thread(target=inference_thread_fn, args=(detector, frame_q, stop_event, stats), daemon=True)
    cap_t.start()
    inf_t.start()

    logging.info("Started capture + inference threads. Press Ctrl-C to stop.")
    try:
        while True:
            # print live stats once per 0.5s
            print(f"\rFPS: {stats['fps']:.2f} | Detections/sec: {stats['detections_per_sec']} | "
                  f"avg_pre: {stats['avg_pre']*1000:.1f}ms avg_inf: {stats['avg_infer']*1000:.1f}ms",
                  end="", flush=True)
            time.sleep(0.5)
    except KeyboardInterrupt:
        logging.info("\nStopping...")
        stop_event.set()
        cap_t.join(timeout=1.0)
        inf_t.join(timeout=1.0)
        try:
            picam2.stop()
        except Exception:
            pass

        # Print final model time stats if available
        try:
            # time_stats might be a method or property depending on SDK version
            ts = detector.model.time_stats() if callable(detector.model.time_stats) else detector.model.time_stats
            logging.info("Model time stats: %s", ts)
        except Exception:
            logging.info("No model time stats available on this SDK version.")

        logging.info("Exiting.")

if __name__ == "__main__":
    main()
