"""
Lightweight, production-oriented vision showcase framework.

This is a patched version of the original `showcase_framework.py` with a fix for
`OpenCVFileSink` so the output video respects the requested FPS.

Key changes:
 - GStreamer pipeline now includes explicit caps for framerate, resolution and
   uses `videorate` and `h264parse` to ensure correct timing when using GStreamer.
 - Added timestamping / `appsrc` properties to make the pipeline "is-live" and
   accept time-based frames.
 - On top of the pipeline fix, the sink uses a time-based write throttle to
   ensure frames are written at (approximately) the requested FPS. This acts as
   a safety-net when GStreamer is not available or the fallback writer is used.
 - A small helper converts float FPS to a rational string (e.g. `30/1` or
   `30000/1001`) to satisfy GStreamer caps.
 - The rest of the framework is unchanged.

If you still see FPS mismatch after this change, try running with the fallback
XVID writer (rename output to .avi) or verify the pipeline has GStreamer with
H.264 support installed on the host.
"""

from __future__ import annotations

import abc
import configparser
import logging
import math
import threading
import time
import queue
from dataclasses import dataclass
from typing import Any, Dict, Iterable, List, Optional, Tuple

import cv2
import numpy as np

logger = logging.getLogger("showcase_framework")
logging.basicConfig(level=logging.INFO, format="[%(levelname)s] %(message)s")


# ----------------------------- Core dataclasses / types -----------------------------

@dataclass
class Detection:
    """Normalized representation of a single detection.

    bbox: (x1, y1, x2, y2) in *absolute pixel coordinates* when `normalized` is False,
          otherwise values are 0..1 normalized coordinates.
    score: optional confidence score (0..1 or model's native scale)
    label: optional string label (e.g. 'person', 'car')
    raw: the original model item for debugging
    """
    bbox: Tuple[float, float, float, float]
    score: Optional[float] = None
    label: Optional[str] = None
    raw: Any = None


@dataclass
class FramePacket:
    """One packet that flows through the pipeline."""
    frame_rgb: np.ndarray  # HxWx3 uint8 RGB
    model_out: Any
    timestamp: float
    frame_idx: int
    meta: Dict[str, Any]


# ----------------------------- Abstract base classes -----------------------------

class FrameSource(abc.ABC):
    """Abstract frame provider.

    Implementations must provide `read()` to return RGB numpy frames (HxWx3) or None on EOF.
    """

    @abc.abstractmethod
    def read(self) -> Optional[np.ndarray]:
        pass

    @abc.abstractmethod
    def start(self) -> None:
        pass

    @abc.abstractmethod
    def stop(self) -> None:
        pass


class InferenceModel(abc.ABC):
    """Abstract inference model.

    Implement `infer(frame_rgb)` and return model-specific output. The framework will
    normalize that output into `Detection` objects for processors via helpers.
    """

    @abc.abstractmethod
    def infer(self, frame_rgb: np.ndarray) -> Any:
        pass

    def warmup(self, frame_rgb: np.ndarray) -> None:
        """Optional warmup - default is no-op."""
        return


class FrameProcessor(abc.ABC):
    """Business-logic class. Implement `process(packet)` to return processed RGB frame.

    The base class provides helpers: `parse_detections` and `draw_boxes` to avoid
    reimplementing parsing/drawing across showcases.
    """

    def __init__(self, conf_threshold: float = 0.0):
        self.conf_threshold = float(conf_threshold or 0.0)

    @abc.abstractmethod
    def process(self, pkt: FramePacket) -> np.ndarray:
        """Return processed RGB frame (HxWx3 uint8)."""
        pass

    # ----------------- Helpers for processors -----------------
    def parse_detections(self, model_out: Any, frame_shape: Tuple[int, int]) -> List[Detection]:
        """Normalize arbitrary model outputs into a list of Detection.

        Accepts common shapes:
          - list/tuple of dicts or arrays
          - dict with keys 'results', 'detections', 'objects', 'boxes'
          - numpy arrays (N,4) or (N,5)
          - single dict with 'bbox'/'box' and optional 'score'/'confidence' and 'label'/'class'
        """
        h, w = frame_shape
        raw_items = _explode_model_output(model_out)
        dets: List[Detection] = []
        for item in raw_items:
            try:
                bbox_raw, score, label = _extract_bbox_score_label(item)
                if bbox_raw is None:
                    continue
                x1, y1, x2, y2 = _to_xyxy_pixels(bbox_raw, w, h)
                det = Detection(bbox=(x1, y1, x2, y2), score=score, label=label, raw=item)
                if det.score is None or det.score >= self.conf_threshold:
                    dets.append(det)
            except Exception as e:
                logger.debug("Failed parsing item %s: %s", getattr(item, '__repr__', lambda: item)(), e)
                continue
        return dets

    def draw_boxes(
        self,
        frame_rgb: np.ndarray,
        detections: Iterable[Detection],
        label_field: bool = True,
        score_field: bool = True,
        thickness: int = 2,
    ) -> np.ndarray:
        """Draw boxes (in-place on a copy) and return RGB frame.

        Colors are generated deterministically per label to be stable across frames.
        """
        out = frame_rgb.copy()
        if out.dtype != np.uint8:
            out = (np.clip(out, 0, 255)).astype(np.uint8)

        # OpenCV expects BGR for drawing
        bgr = cv2.cvtColor(out, cv2.COLOR_RGB2BGR)
        h, w = out.shape[:2]

        for det in detections:
            x1, y1, x2, y2 = int(det.bbox[0]), int(det.bbox[1]), int(det.bbox[2]), int(det.bbox[3])
            # clamp
            x1, x2 = max(0, min(w - 1, x1)), max(0, min(w - 1, x2))
            y1, y2 = max(0, min(h - 1, y1)), max(0, min(h - 1, y2))
            label = det.label or ""
            color = _label_color(label)
            try:
                cv2.rectangle(bgr, (x1, y1), (x2, y2), color, thickness)
                caption_parts = []
                if label_field and label:
                    caption_parts.append(str(label))
                if score_field and det.score is not None:
                    try:
                        caption_parts.append(f"{float(det.score):.2f}")
                    except Exception:
                        caption_parts.append(str(det.score))
                if caption_parts:
                    caption = " ".join(caption_parts)
                    cv2.putText(bgr, caption, (x1, max(0, y1 - 6)), cv2.FONT_HERSHEY_SIMPLEX, 0.5, color, 1)
            except Exception as e:
                logger.debug("Failed to draw box: %s", e)
                continue

        return cv2.cvtColor(bgr, cv2.COLOR_BGR2RGB)


class FrameSink(abc.ABC):
    """Abstract sink - e.g. file writer, RTSP, cloud upload."""

    @abc.abstractmethod
    def start(self) -> None:
        pass

    @abc.abstractmethod
    def put(self, frame_rgb: np.ndarray) -> None:
        pass

    @abc.abstractmethod
    def stop(self) -> None:
        pass


# ----------------------------- Utilities: parsing + colors -----------------------------

def _explode_model_output(model_out: Any) -> List[Any]:
    """Return a flat list of items to interpret as detections.

    This unwraps common container keys ('results', 'detections', 'objects', 'boxes') and
    accepts lists, tuples, numpy arrays, and single dicts.
    """
    if model_out is None:
        return []

    # If an object exposes .results or .detections use that
    for key in ("results", "detections", "objects", "boxes"):
        if hasattr(model_out, key):
            try:
                container = getattr(model_out, key)
                return list(container) if container is not None else []
            except Exception:
                break

    # If dict with one of those keys
    if isinstance(model_out, dict):
        for key in ("results", "detections", "objects", "boxes"):
            if key in model_out and isinstance(model_out[key], (list, tuple, np.ndarray)):
                return list(model_out[key])
        # Single dict that may itself be a detection
        return [model_out]

    # If list/tuple/ndarray: expand to list
    if isinstance(model_out, (list, tuple)):
        return list(model_out)

    if isinstance(model_out, np.ndarray):
        if model_out.ndim == 2 and model_out.shape[1] in (4, 5):
            return [row for row in model_out]
        if model_out.ndim == 1 and model_out.size >= 4:
            return [model_out]
        # fallback: try to convert rows
        try:
            return [row for row in model_out]
        except Exception:
            return [model_out]

    # Unknown object: wrap
    return [model_out]


def _extract_bbox_score_label(item: Any) -> Tuple[Optional[Any], Optional[float], Optional[str]]:
    """Try to pull bbox, score, and label from an item.

    Bbox returned in raw form; caller will convert to pixels.
    """
    # If it's already a Detection
    if isinstance(item, Detection):
        return item.bbox, item.score, item.label

    # Numpy row or list-like: [x1,y1,x2,y2,(score),(class/label)]
    if isinstance(item, (list, tuple, np.ndarray)):
        arr = np.asarray(item).flatten()
        if arr.size >= 4:
            bbox = arr[:4].tolist()
            score = float(arr[4]) if arr.size >= 5 else None
            label = None
            if arr.size >= 6:
                label = str(arr[5])
            return bbox, score, label
        return None, None, None

    # Dict-like
    if isinstance(item, dict):
        # Score keys
        score = None
        for k in ("score", "confidence", "prob", "conf"):
            if k in item:
                try:
                    score = float(item[k])
                except Exception:
                    score = None
                break
        # Label/class keys
        label = None
        for k in ("label", "class", "class_name", "category"):
            if k in item:
                label = str(item[k])
                break
        # bbox keys
        if "bbox" in item:
            return item["bbox"], score, label
        if "box" in item:
            return item["box"], score, label
        # individual fields
        if all(k in item for k in ("x1", "y1", "x2", "y2")):
            return (item["x1"], item["y1"], item["x2"], item["y2"]), score, label
        if all(k in item for k in ("x", "y", "w", "h")):
            return (item["x"], item["y"], item["x"] + item["w"], item["y"] + item["h"]), score, label
        # Sometimes a single dict holds nested detections (handled earlier) - otherwise unknown
        return None, score, label

    # Unknown item
    return None, None, None


def _to_xyxy_pixels(bbox_raw: Any, frame_w: int, frame_h: int) -> Tuple[int, int, int, int]:
    """Convert bbox in raw form to absolute pixel (x1,y1,x2,y2).

    Accepts:
      - sequences of 4 numbers: [x1,y1,x2,y2] or [x,y,w,h] or normalized 0..1 versions
      - dicts handled earlier (we receive sequences here)
    """
    arr = np.asarray(bbox_raw, dtype=float).flatten()
    if arr.size < 4:
        raise ValueError("bbox must have at least 4 numeric values")

    # If they look normalized (all values in 0..1.05) treat as normalized xyxy
    if np.all(arr[:4] >= 0.0) and np.all(arr[:4] <= 1.05):
        x1 = int(round(float(arr[0]) * frame_w))
        y1 = int(round(float(arr[1]) * frame_h))
        x2 = int(round(float(arr[2]) * frame_w))
        y2 = int(round(float(arr[3]) * frame_h))
        return x1, y1, x2, y2

    # Otherwise interpret as absolute coords, but detect xywh if arr[2] < arr[0] (common when models return x,w)
    x0, y0, x2_or_w, y2_or_h = float(arr[0]), float(arr[1]), float(arr[2]), float(arr[3])
    # Heuristic: if x2_or_w < x0 or y2_or_h < y0 -> treat as x,y,w,h
    if (x2_or_w < x0) or (y2_or_h < y0):
        x1 = int(round(x0))
        y1 = int(round(y0))
        x2 = int(round(x0 + x2_or_w))
        y2 = int(round(y0 + y2_or_h))
        return x1, y1, x2, y2

    # Otherwise assume x1,y1,x2,y2 absolute
    x1 = int(round(x0))
    y1 = int(round(y0))
    x2 = int(round(x2_or_w))
    y2 = int(round(y2_or_h))
    return x1, y1, x2, y2


def _label_color(label: Optional[str]) -> Tuple[int, int, int]:
    """Deterministic color for a label. Returns a BGR tuple for OpenCV drawing.

    We use a hash -> HSV -> BGR mapping to give varied but steady colors.
    """
    if not label:
        # green-ish default
        return (0, 200, 0)
    h = abs(hash(label)) % 360
    # convert hue to BGR via simple HSV->BGR (approx)
    import colorsys

    r, g, b = colorsys.hsv_to_rgb(h / 360.0, 0.7, 0.9)
    # Convert 0..1 -> 0..255 and BGR ordering
    return (int(b * 255), int(g * 255), int(r * 255))


# ----------------------------- Concrete helpers / simple implementations -----------------------------

class VideoFileSource(FrameSource):
    """Video file source using OpenCV.VideoCapture. Returns RGB frames.

    New: realtime=True will pace reads to the file's FPS (emulates a live camera).
    """

    def __init__(self, path: str, resize: Optional[Tuple[int, int]] = None, realtime: bool = False, speed: float = 1.0):
        self.path = path
        self.resize = resize
        self.realtime = bool(realtime)
        self.speed = float(speed) if speed > 0 else 1.0
        self._cap = None
        self._stopped = True
        self._frame_interval = None
        self._next_frame_time = None

    def start(self) -> None:
        self._cap = cv2.VideoCapture(self.path)
        if not self._cap.isOpened():
            raise RuntimeError(f"Failed to open video file: {self.path}")

        # Determine fps; fallback to 30 if unknown
        fps = self._cap.get(cv2.CAP_PROP_FPS) or 0.0
        if fps <= 1e-3:
            fps = 30.0
        fps *= self.speed
        self._frame_interval = 1.0 / float(fps)
        self._next_frame_time = None
        self._stopped = False
        logger.info("VideoFileSource started: %s (realtime=%s, fps=%.3f)", self.path, self.realtime, fps)

    def read(self) -> Optional[np.ndarray]:
        if self._cap is None or self._stopped:
            return None

        ret, frame_bgr = self._cap.read()
        if not ret:
            return None

        if self.resize is not None:
            frame_bgr = cv2.resize(frame_bgr, self.resize)

        frame_rgb = cv2.cvtColor(frame_bgr, cv2.COLOR_BGR2RGB)

        if self.realtime and self._frame_interval is not None:
            now = time.monotonic()
            if self._next_frame_time is None:
                # first frame: schedule next time
                self._next_frame_time = now + self._frame_interval
            else:
                # sleep until the scheduled time (if we are early)
                to_wait = self._next_frame_time - now
                if to_wait > 0:
                    time.sleep(to_wait)
                    # after sleeping, set next frame time relative to previous schedule
                    self._next_frame_time += self._frame_interval
                else:
                    # we're late; advance next_frame_time to now + interval (avoid huge drift)
                    self._next_frame_time = now + self._frame_interval

        return frame_rgb

    def stop(self) -> None:
        self._stopped = True
        try:
            if self._cap is not None:
                self._cap.release()
                self._cap = None
        except Exception:
            pass
        logger.info("VideoFileSource stopped")



class DummyModel(InferenceModel):
    """A tiny model that returns synthetic detections for testing/showcase."""

    def infer(self, frame_rgb: np.ndarray) -> Any:
        h, w = frame_rgb.shape[:2]
        # produce two boxes: center and top-left
        cx, cy = w // 2, h // 2
        box1 = [cx - w * 0.15, cy - h * 0.15, cx + w * 0.15, cy + h * 0.15]
        box2 = [w * 0.1, h * 0.1, w * 0.3, h * 0.3]
        return [
            {"bbox": box1, "score": 0.9, "label": "dummy_center"},
            {"bbox": box2, "score": 0.6, "label": "dummy_tl"},
        ]


class OpenCVFileSink(FrameSink):
    """Robust file sink for Raspberry Pi using GStreamer for MP4 (H.264) encoding.

    This class fixes a common problem where the output file does not respect the
    requested FPS by:
      - providing explicit caps (including framerate) to the GStreamer pipeline
      - adding `videorate` and `h264parse` where needed
      - and applying a time-based throttle to writes as a fallback

    Note: GStreamer must be available and built with the necessary plugins for
    H.264 encoding for the GStreamer path to work. If the GStreamer writer fails
    we fall back to a simple XVID AVI writer which respects the FPS argument.
    """

    def __init__(self, path: str, fps: float = 15.0):
        self.path = path
        self.fps = float(fps)
        self.width = None
        self.height = None
        self._q: queue.Queue = queue.Queue(maxsize=512)
        self._stop_ev = threading.Event()
        self._thread: Optional[threading.Thread] = None
        self._writer: Optional[cv2.VideoWriter] = None
        self._last_write_ts: Optional[float] = None
        # precompute frame interval
        self._frame_interval = 1.0 / max(1e-6, float(self.fps))

            # write counters & timing for monitoring
        self._written_count = 0
        self._written_lock = threading.Lock()
        self._writer_start_ts: Optional[float] = None
    def _fps_to_gst_fraction(self) -> str:
        # Convert float fps to a rational string for GStreamer caps, e.g. "30/1" or "30000/1001"
        try:
            from fractions import Fraction
            f = Fraction(self.fps).limit_denominator(1001)
            return f"{f.numerator}/{f.denominator}"
        except Exception:
            return f"{int(round(self.fps))}/1"

    def start(self):
        self._stop_ev.clear()
        self._thread = threading.Thread(target=self._run, daemon=True)
        self._thread.start()
        logger.info("OpenCVFileSink started: %s", self.path)

    def put(self, frame_rgb: np.ndarray):
        # Lazy init writer on first frame
        if self._writer is None:
            h, w = frame_rgb.shape[:2]
            self.width, self.height = w, h
            # Build GStreamer pipeline with explicit caps for framerate and resolution
            gst_fps = self._fps_to_gst_fraction()
            gst_str = (
                f"appsrc is-live=true format=time do-timestamp=true ! "
                f"video/x-raw,format=BGR,framerate={gst_fps},width={w},height={h} ! "
                f"videoconvert ! videorate ! x264enc tune=zerolatency bitrate=500 speed-preset=superfast ! "
                f"h264parse ! mp4mux ! filesink location={self.path}"
            )
            try:
                self._writer = cv2.VideoWriter(
                    gst_str,
                    cv2.CAP_GSTREAMER,
                    0,
                    float(self.fps),
                    (w, h),
                )
            except Exception:
                self._writer = None

            if self._writer is None or not self._writer.isOpened():
                # fallback to XVID AVI
                logger.warning("GStreamer H.264 failed or unavailable, falling back to XVID AVI")
                self.path = self.path.rsplit(".", 1)[0] + ".avi"
                fourcc = cv2.VideoWriter_fourcc(*"XVID")
                self._writer = cv2.VideoWriter(self.path, fourcc, float(self.fps), (w, h))

            if not self._writer.isOpened():
                raise RuntimeError(f"Failed to create video writer for {self.path}")

            # reset last write timestamp so the time throttle starts fresh
            self._last_write_ts = None
            try:
                # writer accepted: record start time for effective FPS computation
                self._writer_start_ts = time.time()
                with self._written_lock:
                    self._written_count = 0
            except Exception:
                pass

        try:
            self._q.put_nowait(frame_rgb)
        except queue.Full:
            _ = self._q.get_nowait()
            self._q.put_nowait(frame_rgb)

    def _run(self):
        while not self._stop_ev.is_set() or not self._q.empty():
            try:
                frame = self._q.get(timeout=0.5)
            except queue.Empty:
                continue
            try:
                # resize if needed
                if frame.shape[:2] != (self.height, self.width):
                    frame = cv2.resize(frame, (self.width, self.height))
                bgr = cv2.cvtColor(frame, cv2.COLOR_RGB2BGR)

                # Time-based write throttle to make sure we don't write frames faster
                # than the requested FPS. This helps when the input/source is producing
                # frames faster than the desired output FPS or when the writer does
                # not enforce timing.
                now = time.time()
                if self._last_write_ts is not None:
                    elapsed = now - self._last_write_ts
                    if elapsed < self._frame_interval:
                        # Sleep only for the remaining time slice
                        to_wait = self._frame_interval - elapsed
                        # If to_wait is large, cap it to avoid blocking for long durations
                        if to_wait > 0:
                            time.sleep(to_wait)

                # Write the frame
                self._writer.write(bgr)
                self._last_write_ts = time.time()
                # update write counters for monitoring
                try:
                    with self._written_lock:
                        self._written_count += 1
                        wc = int(self._written_count)
                    if wc % 30 == 0:
                        if self._writer_start_ts:
                            elapsed = time.time() - self._writer_start_ts
                            if elapsed > 0.001:
                                fps_eff = float(wc) / float(elapsed)
                                logger.info("Sink wrote %d frames (effective fps: %.2f) to %s", wc, fps_eff, self.path)
                except Exception:
                    pass

            except Exception as e:
                logger.error("Sink write error: %s", e)
            finally:
                try:
                    self._q.task_done()
                except Exception:
                    pass
        if self._writer:
            try:
                self._writer.release()
            except Exception:
                pass
        logger.info("OpenCVFileSink finished writing")

    def stop(self):
        self._stop_ev.set()
        if self._thread is not None:
            self._thread.join(timeout=10.0)
        logger.info("OpenCVFileSink stopped")


class Picamera2Source(FrameSource):
    """FrameSource using Picamera2 on Raspberry Pi.
    Outputs RGB frames (HxWx3 uint8).
    """

    def __init__(self, size=(1280, 720), format="RGB888"):
        from picamera2 import Picamera2
        from libcamera import controls

        self.size = size
        self.format = format
        self.picam2 = Picamera2()
        self._stopped = True

        # Configure camera
        config = self.picam2.create_preview_configuration(
            main={"size": self.size, "format": self.format}
        )
        self.picam2.configure(config)

    def start(self):
        self.picam2.start()
        self._stopped = False
        logger.info("Picamera2Source started: size=%s format=%s", self.size, self.format)

    def read(self) -> Optional[np.ndarray]:
        if self._stopped:
            return None
        try:
            frame = self.picam2.capture_array()
            # Already in RGB888 format
            return frame
        except Exception as e:
            logger.error("Picamera2 capture error: %s", e)
            return None

    def stop(self):
        self._stopped = True
        try:
            self.picam2.stop()
        except Exception:
            pass
        logger.info("Picamera2Source stopped")


class DegirumRetinaFaceModel(InferenceModel):
    """
    Degirum RetinaFace model adapter for the showcase framework.
    Requires `degirum` package and a local model zoo path.

    Usage:
        model = DegirumRetinaFaceModel(
            model_name="retinaface_mobilenet--736x1280_quant_hailort_hailo8l_1",
            zoo_path="~/degirum-zoo"
        )
    """

    def __init__(self, model_name: str, zoo_path: Optional[str] = None):
        try:
            import degirum as dg
        except ImportError:
            raise RuntimeError(
                "The 'degirum' package is not installed. Install it or use DummyModel for testing."
            )
        import os

        self.dg = dg
        self.model_name = model_name
        self.zoo_path = os.path.expanduser(zoo_path) if zoo_path else None
        self.zoo = None
        self.model = None

    def load(self) -> None:
        logger.info("Connecting to Degirum local zoo: %s", self.zoo_path)
        self.zoo = self.dg.connect(self.dg.LOCAL, self.zoo_path)
        self.model = self.zoo.load_model(self.model_name)
        try:
            # Some runtimes accept this hint for correct color ordering
            self.model.input_numpy_colorspace = "RGB"
        except Exception:
            pass
        logger.info("Degirum model loaded: %s", self.model_name)

    def infer(self, frame_rgb: np.ndarray) -> Any:
        if self.model is None:
            self.load()
        return self.model(frame_rgb)

    def warmup(self, frame_rgb: np.ndarray) -> None:
        """Optional warmup to prime the accelerator."""
        if self.model is None:
            self.load()
        try:
            _ = self.model(frame_rgb)
        except Exception as e:
            logger.warning("Degirum warmup failed: %s", e)



# ----------------------------- Pipeline -----------------------------

class BufferedPipeline:
    """Capture -> Infer -> Process -> Sink pipeline with bounded queues.

    Attributes exposed for compatibility with existing scripts: preview_q (frames for display)
    """

    def __init__(
        self,
        source: FrameSource,
        model: InferenceModel,
        processor: FrameProcessor,
        sink: Optional[FrameSink] = None,
        input_q_size: int = 8,
        proc_q_size: int = 8,
        save_q_size: int = 128,
        preview_q_size: int = 1,
        infer_workers: int = 1,
    ) -> None:
        self.source = source
        self.model = model
        self.processor = processor
        self.sink = sink

        self.input_q: queue.Queue = queue.Queue(maxsize=max(1, int(input_q_size)))
        self.proc_q: queue.Queue = queue.Queue(maxsize=max(1, int(proc_q_size)))
        self.save_q: queue.Queue = queue.Queue(maxsize=max(1, int(save_q_size)))
        self.preview_q: queue.Queue = queue.Queue(maxsize=max(1, int(preview_q_size)))

        self._threads: List[threading.Thread] = []
        self._stop_ev = threading.Event()
        self._frame_idx = 0
        self._infer_workers = max(1, int(infer_workers))


        # Counters for monitoring progress
        self._counter_lock = threading.Lock()
        self._captured_count = 0
        self._inferred_count = 0
        self._processed_count = 0
        self._sink_handed_count = 0
        self._monitor_thread: Optional[threading.Thread] = None
    # ---------------- lifecycle ----------------
    def start(self) -> None:
        logger.info("Starting pipeline")
        self.source.start()
        try:
            # attempt warmup to initialize weights/hardware
            dummy = self.source.read()
            if dummy is not None:
                self.model.warmup(dummy)
                # push dummy back to source by storing it for first read loop? We can't easily push back,
                # so we continue normally - this read consumes one frame from source. It's acceptable.
        except Exception:
            pass

        # start sink
        if self.sink is not None:
            # estimate size from first frame if possible
            self.sink.start()

        # capture thread
        t_cap = threading.Thread(target=self._capture_loop, daemon=True)
        t_cap.start()
        self._threads.append(t_cap)

        # inference threads
        for _ in range(self._infer_workers):
            t_inf = threading.Thread(target=self._inference_loop, daemon=True)
            t_inf.start()
            self._threads.append(t_inf)

        # processing thread
        t_proc = threading.Thread(target=self._processing_loop, daemon=True)
        t_proc.start()
        self._threads.append(t_proc)

        # sink thread
        if self.sink is not None:
            t_sink = threading.Thread(target=self._sink_loop, daemon=True)
            t_sink.start()
            self._threads.append(t_sink)

        # monitoring thread (logs counts & queue sizes periodically)
        try:
            self._monitor_thread = threading.Thread(target=self._monitor_loop, daemon=True)
            self._monitor_thread.start()
            self._threads.append(self._monitor_thread)
        except Exception:
            pass

    def stop_capture(self) -> None:
        logger.info("Stopping capture (pipeline)")
        self._stop_ev.set()
        try:
            self.source.stop()
        except Exception:
            pass

    def stop_all(self) -> None:
        logger.info("Stopping all pipeline threads")
        self._stop_ev.set()
        try:
            self.source.stop()
        except Exception:
            pass
        try:
            if self.sink is not None:
                self.sink.stop()
        except Exception:
            pass

    def join(self, timeout: Optional[float] = None) -> None:
        # wait for threads to finish
        start = time.time()
        for t in self._threads:
            remaining = None
            if timeout is not None:
                elapsed = time.time() - start
                remaining = max(0.0, timeout - elapsed)
            t.join(timeout=remaining)

    def _monitor_loop(self):
        """Background monitor that logs pipeline progress periodically."""
        interval = 5.0
        try:
            while not self._stop_ev.is_set():
                time.sleep(interval)
                try:
                    with self._counter_lock:
                        cap = int(self._captured_count)
                        inf = int(self._inferred_count)
                        proc = int(self._processed_count)
                        handed = int(self._sink_handed_count)
                except Exception:
                    cap = inf = proc = handed = 0
                logger.info(
                    "Pipeline progress: captured=%d inferred=%d processed=%d sink_handed=%d | queues: input=%d proc=%d save=%d preview=%d",
                    cap, inf, proc, handed, self.input_q.qsize(), self.proc_q.qsize(), self.save_q.qsize(), self.preview_q.qsize(),
                )
            logger.info("Monitor thread exiting")
        except Exception as e:
            logger.exception("Monitor loop error: %s", e)

    # ---------------- core loops ----------------
    def _capture_loop(self) -> None:
        logger.info("Capture loop started")
        while not self._stop_ev.is_set():
            try:
                frame_rgb = self.source.read()
            except Exception as e:
                logger.error("Source read error: %s", e)
                break
            if frame_rgb is None:
                # EOF or no frame - break the loop
                logger.info("No more frames from source")
                self._stop_ev.set()
                break

            # push to input queue; if full drop oldest to keep low-latency preview
            try:
                # increment captured counter
                try:
                    with self._counter_lock:
                        self._captured_count += 1
                except Exception:
                    pass
                self.input_q.put_nowait((self._frame_idx, frame_rgb, time.time()))
            except queue.Full:
                try:
                    _ = self.input_q.get_nowait()
                    self.input_q.put_nowait((self._frame_idx, frame_rgb, time.time()))
                except Exception:
                    pass

            self._frame_idx += 1

        logger.info("Capture loop finished")

    def _inference_loop(self) -> None:
        logger.info("Inference worker started")
        while not self._stop_ev.is_set() or not self.input_q.empty():
            try:
                idx, frame_rgb, ts = self.input_q.get(timeout=0.5)
            except queue.Empty:
                continue
            try:
                model_out = self.model.infer(frame_rgb)
            except Exception as e:
                logger.exception("Model inference error: %s", e)
                model_out = None

            pkt = FramePacket(frame_rgb=frame_rgb, model_out=model_out, timestamp=ts, frame_idx=idx, meta={})
            # increment inferred counter
            try:
                with self._counter_lock:
                    self._inferred_count += 1
            except Exception:
                pass
            # push to proc queue (drop oldest if full)
            try:
                self.proc_q.put_nowait(pkt)
            except queue.Full:
                try:
                    _ = self.proc_q.get_nowait()
                    self.proc_q.put_nowait(pkt)
                except Exception:
                    pass
            finally:
                try:
                    self.input_q.task_done()
                except Exception:
                    pass

        logger.info("Inference worker finished")

    def _processing_loop(self) -> None:
        logger.info("Processing worker started")
        while not self._stop_ev.is_set() or not self.proc_q.empty():
            try:
                pkt = self.proc_q.get(timeout=0.5)
            except queue.Empty:
                continue
            try:
                processed_rgb = self.processor.process(pkt)
            except Exception as e:
                logger.exception("Processor error: %s", e)
                processed_rgb = pkt.frame_rgb

            # increment processed counter
            try:
                with self._counter_lock:
                    self._processed_count += 1
            except Exception:
                pass

            # Put frame for preview (drop oldest)
            try:
                self.preview_q.put_nowait(processed_rgb)
            except queue.Full:
                try:
                    _ = self.preview_q.get_nowait()
                    self.preview_q.put_nowait(processed_rgb)
                except Exception:
                    pass

            # Put frame to save queue if sink present
            if self.sink is not None:
                try:
                    self.save_q.put_nowait(processed_rgb)
                except queue.Full:
                    # For save we prefer oldest drop policy too - drop oldest.
                    try:
                        _ = self.save_q.get_nowait()
                        self.save_q.put_nowait(processed_rgb)
                    except Exception:
                        pass

            try:
                self.proc_q.task_done()
            except Exception:
                pass

        logger.info("Processing worker finished")

    def _sink_loop(self) -> None:
        logger.info("Sink worker started")
        while not self._stop_ev.is_set() or not self.save_q.empty():
            try:
                frame_rgb = self.save_q.get(timeout=0.5)
            except queue.Empty:
                continue
            try:
                # increment counter for frames handed to sink
                try:
                    with self._counter_lock:
                        self._sink_handed_count += 1
                except Exception:
                    pass
                self.sink.put(frame_rgb)
            except Exception as e:
                logger.exception("Sink put error: %s", e)
            finally:
                try:
                    self.save_q.task_done()
                except Exception:
                    pass
        logger.info("Sink worker finished")


# ----------------------------- Example: run_picam_showcase.py (business-only) -----------------------------
# The following is an example usage that should live in your showcase script.
# It demonstrates a minimal 'business-only' scenario: draw ALL detections for any model.

if __name__ == "__main__":
    import argparse

    ap = argparse.ArgumentParser(description="Run a generic detection showcase (business-only scenario)")
    ap.add_argument("--video-file", type=str, default=None, help="Optional path to video file (instead of camera)")
    ap.add_argument("--out", type=str, default="output_new.mp4", help="Where to save processed video")
    ap.add_argument("--conf", type=float, default=0.0, help="Minimum confidence to show")
    ap.add_argument("--input-q-size", type=int, default=8)
    ap.add_argument("--proc-q-size", type=int, default=8)
    ap.add_argument("--save-q-size", type=int, default=64)
    ap.add_argument("--realtime", action="store_true", help="pace input reads to file FPS (emulate live camera)")
    ap.add_argument("--preview-q-size", type=int, default=1)
    ap.add_argument("--fps", type=float, default=15.0)
    ap.add_argument("--debug", action="store_true")
    ap.add_argument("--headless", action="store_true", help="Run without preview window (headless)")
    args = ap.parse_args()

    if args.debug:
        logging.getLogger().setLevel(logging.DEBUG)

    # Choose source
    if args.video_file:
        source = VideoFileSource(args.video_file,realtime=args.realtime)
    else:
        try:
            source = Picamera2Source(size=(1280, 720))
        except ImportError:
            logger.error("Picamera2 not available. Please install it or provide --video-file.")
            exit(1)

    # Choose model (try to use your DeGirum model if available, else DummyModel)
    try:
        model = DegirumRetinaFaceModel(
            #model_name="yolov8n_relu6_car--640x640_quant_hailort_multidevice_1",
            #model_name="hand_landmark_lite--224x224_quant_hailort_hailo8l_1",
            model_name="retinaface_mobilenet--736x1280_quant_hailort_hailo8l_1",
            zoo_path="~/degirum-zoo"  # or None if using remote connection
            # connection_str="hostname_or_ip:port"  # if using remote zoo
        )
    except Exception as e:
        logger.warning("Falling back to DummyModel: %s", e)
        model = DummyModel()


    # Business-only scenario class (developer writes only this)
    class DrawAllDetectionsScenario(FrameProcessor):
        def __init__(self, conf_threshold: float = 0.0):
            super().__init__(conf_threshold=conf_threshold)

        def process(self, pkt: FramePacket) -> np.ndarray:
            # parse detections using the robust helper
            h, w = pkt.frame_rgb.shape[:2]
            dets = self.parse_detections(pkt.model_out, (h, w))
            # example business filter: show only objects with label in allowlist (None => all)
            # allowlist = ("car", "person")
            # dets = [d for d in dets if (d.label in allowlist) or (d.label is None)]
            # draw boxes and return the frame
            return self.draw_boxes(pkt.frame_rgb, dets)

    processor = DrawAllDetectionsScenario(conf_threshold=args.conf)

    # sink - use first frame's size to create sink in start (we approximate using a test frame later)
    # for simplicity, pick conservative size: 1280x720 or derive from source
    sample_frame = None
    try:
        # attempt to start source and read a frame for sizing
        source.start()
        sample_frame = source.read()
    except Exception:
        pass

    if sample_frame is None:
        # fallback size
        sw, sh = 1280, 720
        sample_frame = np.zeros((sh, sw, 3), dtype=np.uint8)
    else:
        h, w = sample_frame.shape[:2]
        sw, sh = w, h

    sink = OpenCVFileSink(args.out, fps=args.fps)

    # Create pipeline
    pipeline = BufferedPipeline(
        source=source,
        model=model,
        processor=processor,
        sink=sink,
        input_q_size=args.input_q_size,
        proc_q_size=args.proc_q_size,
        save_q_size=args.save_q_size,
        preview_q_size=args.preview_q_size,
    )

    try:
        pipeline.start()
        if not args.headless:
            window = "Preview (press q to stop)"
            cv2.namedWindow(window, cv2.WINDOW_NORMAL)
        else:
            logger.info("Headless mode enabled: no preview window. Use Ctrl+C to stop or wait for source EOF.")

        while True:
            try:
                frame_rgb = pipeline.preview_q.get(timeout=0.5)
            except Exception:
                frame_rgb = None
            if frame_rgb is not None and not args.headless:
                cv2.imshow(window, cv2.cvtColor(frame_rgb, cv2.COLOR_RGB2BGR))
            # handle quit only in non-headless mode
            if not args.headless:
                if cv2.waitKey(1) & 0xFF == ord("q"):
                    pipeline.stop_capture()
                    break

            # Exit if pipeline is done and queues are drained
            if pipeline._stop_ev.is_set() and pipeline.preview_q.empty() and pipeline.save_q.empty():
                logger.info("Pipeline signaled stop and queues are empty; exiting main loop")
                break

            time.sleep(0.005)

        pipeline.join(timeout=120.0)

    except KeyboardInterrupt:
        pipeline.stop_all()
        pipeline.join(timeout=10.0)
    finally:
        try:
            if not args.headless:
                cv2.destroyAllWindows()
        except Exception:
            pass

# python showcase_framework.py  --video-file "/home/<USER>/jk/dev/hailotest/videos/people1_30s.mp4" --conf 0.5 --headless --realtime
# End of file
