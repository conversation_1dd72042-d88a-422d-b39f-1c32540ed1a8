import time, degirum as dg, os, numpy as np

zoo_path = os.path.expanduser("~/degirum-zoo")
model_name = "yolov8n_relu6_car--640x640_quant_hailort_multidevice_1"

model = dg.connect(dg.LOCAL, zoo_path).load_model(model_name)
#model = dg.connect("localhost").load_model(model_name)

dummy_frame = np.zeros((640, 640, 3), dtype=np.uint8)

start = time.time()
count = 0
while time.time() - start < 5:
    model.predict(dummy_frame)
    count += 1

print(f"Pure inference FPS: {count/5:.2f}")
