from picamera2 import Picamera2
import cv2
import time
import numpy as np

picam2 = Picamera2()
picam2.start()

print("Camera started. Capturing 5 frames...")
for i in range(5):
    frame = picam2.capture_array()
    # Convert from RGB (Picamera2 default) to BGR for OpenCV if needed
    frame_bgr = cv2.cvtColor(frame, cv2.COLOR_RGB2BGR)
    print(f"Captured frame {i+1}, shape: {frame_bgr.shape}")
    time.sleep(1)

picam2.stop()
print("Camera stopped.")
