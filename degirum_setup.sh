#!/bin/bash
# Usage: bash setup_degirum_server.sh

set -e

# Configurable paths
VENV_DIR="$HOME/degirum-env"
ZOODIR="$HOME/Downloads/model_zoo"
SRC1="$HOME/Downloads/arcface_mobilefacenet--112x112_quant_hailort_hailo8l_1"
SRC2="$HOME/Downloads/retinaface_mobilenet--736x1280_quant_hailort_hailo8l_1"

echo "Creating virtual environment at $VENV_DIR..."
python3 -m venv "$VENV_DIR"
source "$VENV_DIR/bin/activate"

echo "Installing DeGirum PySDK..."
pip install degirum --extra-index-url https://degirum.github.io/simple

echo "Preparing model zoo directory at $ZOODIR..."
mkdir -p "$ZOODIR"

echo "Copying model folders..."
cp -r "$SRC1" "$ZOODIR/"
cp -r "$SRC2" "$ZOODIR/"

echo "Model zoo contents:"
ls -R "$ZOODIR"

echo "To start the AI server manually, run:"
echo "  source \"$VENV_DIR/bin/activate\""
echo "  degirum server start --zoo \"$ZOODIR\""
echo "It will run until ENTER is pressed. Default port is 8778."

deactivate
