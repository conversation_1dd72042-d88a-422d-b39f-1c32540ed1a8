#!/usr/bin/env python3
"""
pipeline_simple.py

Minimal, reusable buffered pipeline for camera/video -> model -> save workflow.

Features:
- Capture -> input_q -> processing (model.predict + process_fn) -> save_q -> save (mp4)
- Optional frame sampling (input_fps) to skip frames cheaply in the capture loop
- Preview queue to display latest processed frame
- Stats callback (PipelineStats) emitted periodically and once at shutdown
- Picamera2 and Video file sources supported
- DeGirum model wrapper and DummyModel for testing without DeGirum
- KISS, OOP, documented, and robust shutdown

Usage (high level):
    src = Picamera2Source(size=(1280, 736))  # size is (width, height)
    model = DegirumRetinaFaceModel(name, zoo_path)  # or DummyModel()
    pipeline = BufferedPipelineSimple(
        source=src,
        model=model,
        process_fn=process_fn,
        out_path="out.mp4",
        input_fps=15.0,
        out_fps=None,                 # defaults to input_fps or 25.0
        input_q_size=8,
        save_q_size=128,
        preview_q_size=1,
        stats_callback=on_stats,      # optional
        stats_interval=1.0
    )
    pipeline.start()
    ...
    pipeline.stop_capture()  # graceful stop (drain)
    pipeline.join()
"""

from __future__ import annotations

import os
import time
import logging
import threading
import queue
from dataclasses import dataclass, field
from typing import Optional, Any, Callable, Dict, Tuple, Deque
from collections import deque
import abc

import numpy as np
import cv2

import time
from typing import Tuple, Optional
import numpy as np
from picamera2 import Picamera2
from libcamera import Transform

# Optional libraries
try:
    import degirum as dg
except Exception:
    dg = None

try:
    from picamera2 import Picamera2
except Exception:
    Picamera2 = None


logger = logging.getLogger("pipeline_simple")
logging.basicConfig(level=logging.INFO, format="[%(levelname)s] %(message)s")


# ----------------------
# Data structures
# ----------------------
@dataclass
class FramePacket:
    """
    Encapsulates a frame and metadata passed through queues.
    - frame: HxWx3 RGB uint8 numpy array
    - idx: monotonic packet index
    - ts: timestamp (seconds since epoch) when packet was created
    - meta: free-form dict for scenario/model output
    """
    frame: np.ndarray
    idx: int
    ts: float
    meta: Dict[str, Any] = field(default_factory=dict)


@dataclass(frozen=True)
class PipelineStats:
    """
    Immutable snapshot of pipeline metrics for the stats callback.
    """
    capture_queue_size: int
    save_queue_size: int
    preview_queue_size: int
    processed_frames: int
    dropped_frames: int
    capture_fps: float
    processing_fps: float
    uptime_seconds: float
    timestamp: float


# ----------------------
# Model interfaces
# ----------------------
class BaseModel(abc.ABC):
    """Abstract model interface used by the pipeline."""
    @abc.abstractmethod
    def load(self) -> None:
        raise NotImplementedError

    @abc.abstractmethod
    def predict(self, frame_rgb: np.ndarray) -> Any:
        raise NotImplementedError

    def warmup(self, n: int = 1) -> None:
        """Optional warmup - default short sleep to simulate warmup time."""
        time.sleep(0.01 * n)

    def close(self) -> None:
        """Optional cleanup for the model."""
        return None


class DegirumRetinaFaceModel(BaseModel):
    """
    DeGirum model wrapper. Requires `degirum` package and a local zoo path.
    Usage: DegirumRetinaFaceModel('model_name', '~/degirum-zoo')
    """
    def __init__(self, model_name: str, zoo_path: Optional[str] = None):
        if dg is None:
            raise RuntimeError("degirum not available. Use DummyModel for testing.")
        self.model_name = model_name
        self.zoo_path = os.path.expanduser(zoo_path) if zoo_path else None
        self.zoo = None
        self.model = None

    def load(self) -> None:
        logger.info("Connecting to DeGirum local zoo: %s", self.zoo_path)
        self.zoo = dg.connect(dg.LOCAL, self.zoo_path)
        self.model = self.zoo.load_model(self.model_name)
        try:
            # some runtimes accept this hint
            self.model.input_numpy_colorspace = "RGB"
        except Exception:
            pass
        logger.info("DeGirum model loaded: %s", self.model_name)

    def predict(self, frame_rgb: np.ndarray) -> Any:
        if self.model is None:
            raise RuntimeError("Model not loaded. Call load() first.")
        # Ensure frame is contiguous uint8 RGB
        frame_rgb = np.ascontiguousarray(frame_rgb, dtype=np.uint8)
        return self.model.predict(frame_rgb)

    def close(self) -> None:
        self.model = None
        self.zoo = None


class DummyModel(BaseModel):
    """A no-op model for testing that returns empty results."""
    def load(self) -> None:
        logger.info("DummyModel loaded (no detections).")

    def predict(self, frame_rgb: np.ndarray) -> Any:
        class R: pass
        r = R()
        r.results = []
        return r

    def close(self) -> None:
        return None


# ----------------------
# Sources
# ----------------------


class Picamera2Source:
    """
    Picamera2 wrapper with lores stream for fast processing.

    Args:
      - size: (width, height) tuple for lores. Main stream stays high-res.
    """
    def __init__(self, size: Tuple[int, int] = (640, 480)):
        if Picamera2 is None:
            raise RuntimeError("Picamera2 is not available in this environment.")
        self.picam2 = Picamera2()
        self.size = size
        self.started = False

    @property
    def name(self) -> str:
        return "picam2"

    def start(self) -> None:
        # High-quality main + low-res lores for inference
        cfg = self.picam2.create_video_configuration(
            main={"size": (2028, 1520), "format": "RGB888"},  # HQ
            lores={"size": self.size, "format": "RGB888"},    # Fast inference
            transform=Transform(hflip=0, vflip=0)
        )
        self.picam2.configure(cfg)
        self.picam2.start()
        try:
            self.picam2.set_controls({"AwbEnable": True})
        except Exception:
            pass
        time.sleep(0.2)
        self.started = True

    def read(self) -> Optional[np.ndarray]:
        """
        Returns the lores RGB numpy array for fast inference.
        """
        try:
            # Capture from lores stream for speed
            #return self.picam2.capture_array("main")
            return self.picam2.capture_array("lores")
        except Exception:
            return None

    def stop(self) -> None:
        try:
            if self.started:
                self.picam2.stop()
        except Exception:
            pass
        self.started = False

    def get_fps(self) -> Optional[float]:
        # Picamera2 doesn't always expose FPS consistently
        return None



class VideoFileSource:
    """
    Video file source (for development / testing).
    - Converts frames to RGB before returning.
    """
    def __init__(self, path: str):
        self.path = path
        self.cap = cv2.VideoCapture(path)
        if not self.cap.isOpened():
            raise FileNotFoundError(f"Cannot open video file: {path}")
        self.name = f"video:{path}"

    def start(self) -> None:
        # nothing required
        return None

    def read(self) -> Optional[np.ndarray]:
        ret, frame_bgr = self.cap.read()
        if not ret:
            return None
        return cv2.cvtColor(frame_bgr, cv2.COLOR_BGR2RGB)

    def stop(self) -> None:
        try:
            if self.cap is not None:
                self.cap.release()
        except Exception:
            pass

    def get_fps(self) -> Optional[float]:
        fps = self.cap.get(cv2.CAP_PROP_FPS)
        try:
            fps = float(fps)
            if fps <= 0:
                return None
            return fps
        except Exception:
            return None


# ----------------------
# Buffered pipeline
# ----------------------
class BufferedPipelineSimple:
    """
    Buffered pipeline implementing:
      capture (source.read) -> input_q -> processing (model.predict + process_fn)
      -> preview_q & save_q -> save (VideoWriter)

    Key constructor args:
      - source: object implementing start(), read()->np.ndarray (RGB), stop()
      - model: BaseModel instance (load/predict/close)
      - process_fn: callable(frame_rgb, model_out, pkt)->processed_rgb (uint8 RGB)
      - out_path: output mp4 path
      - input_fps: desired processing FPS (None => process every frame)
      - out_fps: output video fps (defaults to input_fps or 25.0)
      - input_q_size, save_q_size, preview_q_size: queue sizes
      - stats_callback: optional callable(PipelineStats) receiving periodic stats
      - stats_interval: how often to call stats_callback (seconds)
    """

    def __init__(
        self,
        source: Any,
        model: BaseModel,
        process_fn: Callable[[np.ndarray, Any, FramePacket], np.ndarray],
        out_path: str = "out.mp4",
        input_fps: Optional[float] = None,
        out_fps: Optional[float] = None,
        input_q_size: int = 8,
        save_q_size: int = 128,
        preview_q_size: int = 1,
        stats_callback: Optional[Callable[[PipelineStats], None]] = None,
        stats_interval: float = 1.0,
        fourcc: str = "mp4v",
    ):
        # public components
        self.source = source
        self.model = model
        self.process_fn = process_fn
        self.out_path = out_path

        # fps settings
        self.input_fps = float(input_fps) if input_fps is not None else None
        self.out_fps = float(out_fps) if out_fps is not None else (self.input_fps or 25.0)
        self._frame_interval = (1.0 / self.input_fps) if self.input_fps else None

        # queues
        self.input_q: queue.Queue = queue.Queue(maxsize=max(1, input_q_size))
        self.save_q: queue.Queue = queue.Queue(maxsize=max(1, save_q_size))
        self.preview_q: queue.Queue = queue.Queue(maxsize=max(1, preview_q_size))

        # thread control
        self._stop_capture_event = threading.Event()  # graceful capture stop
        self._stop_all_event = threading.Event()      # immediate stop
        self._stats_stop_event = threading.Event()

        self._capture_thread: Optional[threading.Thread] = None
        self._proc_thread: Optional[threading.Thread] = None
        self._save_thread: Optional[threading.Thread] = None
        self._stats_thread: Optional[threading.Thread] = None

        # writer & sizes
        self.writer: Optional[cv2.VideoWriter] = None
        self.out_size: Optional[Tuple[int, int]] = None  # (width, height)

        # counters & timestamps
        self._idx_lock = threading.Lock()
        self._next_idx = 0
        self._processed_frames = 0
        self._dropped_frames = 0
        # keep timestamps in deques for efficient popleft
        self._capture_ts: Deque[float] = deque()
        self._process_ts: Deque[float] = deque()
        self._start_time: Optional[float] = None

        # sampling state
        self._last_enqueue_ts: Optional[float] = None

        # stats
        self._stats_callback = stats_callback
        self._stats_interval = float(stats_interval)
        self._fourcc = fourcc

        # logging
        logger.info("Pipeline created (input_fps=%s out_fps=%s)", self.input_fps, self.out_fps)

    # ---------------------
    # Utility / helpers
    # ---------------------
    def _next_packet(self, frame: np.ndarray) -> FramePacket:
        with self._idx_lock:
            idx = self._next_idx
            self._next_idx += 1
        return FramePacket(frame=frame, idx=idx, ts=time.time())

    def _put_drop_oldest(self, q: queue.Queue, item: Any) -> bool:
        """
        Attempt to put item into q; if full, drop oldest and put.
        Returns True if a drop occurred.
        """
        try:
            q.put_nowait(item)
            return False
        except queue.Full:
            try:
                _ = q.get_nowait()  # drop oldest
                q.put_nowait(item)
                return True
            except Exception:
                # If anything goes wrong, attempt blocking put as fallback
                try:
                    q.put(item, timeout=0.1)
                    return True
                except Exception:
                    return False

    @staticmethod
    def _compute_fps_from_deque(dq: Deque[float], window: float = 5.0) -> float:
        """
        Compute FPS from timestamps within 'window' seconds.
        """
        now = time.time()
        # drop old timestamps
        while dq and dq[0] < now - window:
            dq.popleft()
        if len(dq) < 2:
            return 0.0
        # rate = (n-1)/(t_last - t_first)
        return (len(dq) - 1) / (dq[-1] - dq[0])

    def _should_enqueue_frame(self, ts: float) -> bool:
        """
        Decide whether to process this captured frame based on input_fps sampling.
        This function updates last_enqueue timestamp when it returns True.
        """
        if self.input_fps is None:
            return True
        if self._last_enqueue_ts is None:
            self._last_enqueue_ts = ts
            return True
        if (ts - self._last_enqueue_ts) >= self._frame_interval:
            self._last_enqueue_ts = ts
            return True
        return False

    # -------------------------
    # Lifecycle: start / stop
    # -------------------------
    def start(self) -> None:
        """Load model, start source, initialize writer and threads."""
        logger.info("Starting pipeline...")
        # model
        self.model.load()
        try:
            self.model.warmup(1)
        except Exception:
            pass

        # source
        self.source.start()

        # read first valid frame to set sizes
        first = None
        read_attempts = 0
        while first is None and read_attempts < 300:  # ~3s max wait
            first = self.source.read()
            read_attempts += 1
            if first is None:
                time.sleep(0.01)
        if first is None:
            raise RuntimeError("Could not read initial frame from source")

        # determine out size (VideoWriter needs width,height)
        h, w = first.shape[:2]
        self.out_size = (w, h)

        # initialize video writer
        fourcc = cv2.VideoWriter_fourcc(*self._fourcc)
        self.writer = cv2.VideoWriter(self.out_path, fourcc, float(self.out_fps), self.out_size)
        if not self.writer.isOpened():
            raise RuntimeError(f"Could not open VideoWriter for {self.out_path} (size={self.out_size})")

        # set timing/start
        self._start_time = time.time()
        # enqueue first frame if sampling allows
        ts_now = time.time()
        if self._should_enqueue_frame(ts_now):
            pkt = self._next_packet(first)
            self._put_drop_oldest(self.input_q, pkt)

        # threads
        self._stop_capture_event.clear()
        self._stop_all_event.clear()
        self._stats_stop_event.clear()

        self._capture_thread = threading.Thread(target=self._capture_loop, name="capture", daemon=True)
        self._proc_thread = threading.Thread(target=self._processing_loop, name="process", daemon=True)
        self._save_thread = threading.Thread(target=self._save_loop, name="save", daemon=True)

        self._capture_thread.start()
        self._proc_thread.start()
        self._save_thread.start()

        # stats thread
        if self._stats_callback:
            self._stats_thread = threading.Thread(target=self._stats_loop, name="stats", daemon=False)
            self._stats_thread.start()

        logger.info("Pipeline started: source=%s out=%s", getattr(self.source, "name", "source"), self.out_path)

    def stop_capture(self) -> None:
        """
        Stop capturing new frames gracefully. Pipeline will drain queued frames,
        process them and finish saving.
        """
        logger.info("stop_capture() called — will finish processing queued frames")
        self._stop_capture_event.set()

    def stop_all(self) -> None:
        """
        Force-stop the entire pipeline immediately. This will try to unblock
        worker threads and emit final stats.
        """
        logger.info("stop_all() called — force stopping pipeline")
        self._stop_all_event.set()
        # Try to unblock queues by putting sentinels
        try:
            self.input_q.put_nowait(None)
        except Exception:
            pass
        try:
            self.save_q.put_nowait(None)
        except Exception:
            pass
        # Ensure stats thread will stop
        self._stats_stop_event.set()

    def join(self, timeout: Optional[float] = None) -> None:
        """
        Wait for threads to finish. If stop_capture() was used, this will wait
        until the pipeline drains and saves remaining frames.
        """
        # join worker threads (timeout applied per-thread)
        threads = [self._capture_thread, self._proc_thread, self._save_thread]
        start = time.time()
        for t in threads:
            if t is None:
                continue
            remaining = None
            if timeout is not None:
                elapsed = time.time() - start
                remaining = max(0.0, timeout - elapsed)
            t.join(remaining)

        # signal stats thread to stop and join
        self._stats_stop_event.set()
        if self._stats_thread:
            try:
                self._stats_thread.join(timeout)
            except Exception:
                pass

        # Final emit stats if callback present
        if self._stats_callback:
            try:
                self._emit_stats()
            except Exception:
                pass

        # cleanup: release writer, close model, stop source
        try:
            if self.writer is not None:
                self.writer.release()
                self.writer = None
        except Exception:
            logger.exception("Failed to release VideoWriter")

        try:
            self.model.close()
        except Exception:
            logger.exception("Failed to close model")

        try:
            self.source.stop()
        except Exception:
            logger.exception("Failed to stop source")

        logger.info("Pipeline joined and cleaned up")

    # -------------------------
    # Worker loops
    # -------------------------
    def _capture_loop(self) -> None:
        """Continuously read frames from source and enqueue sampled ones."""
        logger.info("capture thread started")
        try:
            while True:
                if self._stop_all_event.is_set():
                    # immediate stop
                    break

                frame = self.source.read()
                if frame is None:
                    # read failure -> small sleep and retry
                    time.sleep(0.005)
                    # if graceful stop requested and no more frames, break
                    if self._stop_capture_event.is_set():
                        break
                    continue

                ts = time.time()
                # record capture timestamp for stats
                self._capture_ts.append(ts)

                # sampling decision
                if not self._should_enqueue_frame(ts):
                    continue

                pkt = self._next_packet(np.ascontiguousarray(frame))
                dropped = self._put_drop_oldest(self.input_q, pkt)
                if dropped:
                    self._dropped_frames += 1

                # if user asked for graceful stop, break after putting sentinel
                if self._stop_capture_event.is_set():
                    break

            # signal EOF for processing stage (if not already forced)
            try:
                self.input_q.put_nowait(None)
            except Exception:
                # best-effort sentinel
                pass

        except Exception as e:
            logger.exception("capture loop error: %s", e)
        finally:
            logger.info("capture thread exiting")

    def _processing_loop(self) -> None:
        """Get packets from input_q, run model and process_fn, then enqueue for save/preview."""
        logger.info("processing thread started")
        try:
            while True:
                try:
                    pkt = self.input_q.get(timeout=0.5)
                except queue.Empty:
                    if self._stop_all_event.is_set():
                        break
                    continue

                if pkt is None:
                    # EOF sentinel -> propagate to save and exit
                    try:
                        self.save_q.put_nowait(None)
                    except Exception:
                        pass
                    break

                frame_rgb = pkt.frame
                # run model
                try:
                    out = self.model.predict(frame_rgb)
                except Exception as e:
                    logger.warning("model.predict error: %s", e)
                    out = None

                # call scenario-provided process_fn -> must return RGB frame (uint8)
                try:
                    processed = self.process_fn(frame_rgb, out, pkt)
                    # ensure uint8 contiguous RGB
                    processed = np.ascontiguousarray(processed.astype(np.uint8))
                except Exception as e:
                    logger.exception("process_fn raised: %s", e)
                    processed = frame_rgb

                pkt.frame = processed
                self._processed_frames += 1
                self._process_ts.append(time.time())

                # publish preview (drop-oldest)
                self._put_drop_oldest(self.preview_q, processed)

                # enqueue for save (drop-oldest)
                dropped = self._put_drop_oldest(self.save_q, pkt)
                if dropped:
                    self._dropped_frames += 1

            # end while
        except Exception as e:
            logger.exception("processing loop error: %s", e)
        finally:
            logger.info("processing thread exiting")

    def _save_loop(self) -> None:
        """Write frames from save_q to disk using OpenCV VideoWriter."""
        logger.info("save thread started")
        try:
            while True:
                try:
                    pkt = self.save_q.get(timeout=0.5)
                except queue.Empty:
                    if self._stop_all_event.is_set():
                        break
                    continue

                if pkt is None:
                    # EOF sentinel -> stop saving
                    break

                frame_rgb = pkt.frame
                # ensure size
                out_w, out_h = self.out_size
                h, w = frame_rgb.shape[:2]
                if (w, h) != (out_w, out_h):
                    try:
                        frame_rgb = cv2.resize(frame_rgb, (out_w, out_h))
                    except Exception:
                        pass
                # convert to BGR and write
                try:
                    frame_bgr = cv2.cvtColor(frame_rgb, cv2.COLOR_RGB2BGR)
                    if self.writer is not None:
                        self.writer.write(frame_bgr)
                except Exception as e:
                    logger.warning("VideoWriter write failed: %s", e)

        except Exception as e:
            logger.exception("save loop error: %s", e)
        finally:
            # Ensure writer released here to make file consistent as soon as saving finishes
            try:
                if self.writer is not None:
                    self.writer.release()
                    self.writer = None
            except Exception:
                logger.exception("Failed to release VideoWriter in save loop")
            logger.info("save thread exiting")

    # -------------------------
    # Stats thread
    # -------------------------
    def _stats_loop(self) -> None:
        """
        Periodically call the stats callback while pipeline is active.
        The loop stops when `_stats_stop_event` is set by join/stop_all.
        """
        logger.info("stats thread started (interval=%s)", self._stats_interval)
        try:
            while not self._stats_stop_event.is_set():
                try:
                    self._emit_stats()
                except Exception as e:
                    logger.exception("Error emitting stats: %s", e)
                # sleep interruptibly
                self._stats_stop_event.wait(self._stats_interval)
        except Exception as e:
            logger.exception("stats loop error: %s", e)
        finally:
            # Emit a final snapshot when stats thread ends
            try:
                self._emit_stats()
            except Exception:
                pass
            logger.info("stats thread exiting")

    def _emit_stats(self) -> None:
        """Create PipelineStats and call the user-provided callback."""
        now = time.time()
        uptime = (now - self._start_time) if self._start_time else 0.0
        stats = PipelineStats(
            capture_queue_size=self.input_q.qsize(),
            save_queue_size=self.save_q.qsize(),
            preview_queue_size=self.preview_q.qsize(),
            processed_frames=self._processed_frames,
            dropped_frames=self._dropped_frames,
            capture_fps=self._compute_fps_from_deque(self._capture_ts),
            processing_fps=self._compute_fps_from_deque(self._process_ts),
            uptime_seconds=uptime,
            timestamp=now,
        )
        if self._stats_callback:
            try:
                self._stats_callback(stats)
            except Exception:
                logger.exception("stats_callback raised an exception")

    # -------------------------
    # Convenience
    # -------------------------
    def source_fps(self) -> Optional[float]:
        """
        Try to get source-reported FPS (VideoFileSource supports this).
        Returns None if unknown.
        """
        try:
            if hasattr(self.source, "get_fps"):
                return self.source.get_fps()
        except Exception:
            pass
        return None

    def close(self) -> None:
        """Close resources (wrapper for compatibility)."""
        try:
            if self.writer is not None:
                self.writer.release()
                self.writer = None
        except Exception:
            pass
        try:
            self.model.close()
        except Exception:
            pass
        try:
            self.source.stop()
        except Exception:
            pass
