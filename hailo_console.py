import os
import time
import argparse
import logging
from threading import Thread
from queue import Queue

import degirum as dg
from picamera2 import Picamera2

logging.basicConfig(level=logging.INFO, format='[%(levelname)s] %(message)s')


class RetinaFaceDetector:
    def __init__(self, model_name: str, zoo_path: str):
        logging.info(f"Connecting to DeGirum in local mode — zoo: {zoo_path}")
        self.zoo = dg.connect(dg.LOCAL, zoo_path)
        logging.info(f"Loading model: {model_name}")
        self.model = self.zoo.load_model(model_name)
        logging.info("Model loaded successfully.")

    def infer(self, frame_rgb):
        return self.model.predict(frame_rgb)

    def parse_detections(self, outputs):
        # Return number of detections safely
        try:
            return len(outputs.results)
        except Exception:
            return 0


def run_fps_test(width, height):
    zoo_path = os.path.expanduser("~/degirum-zoo")
    model_name = "retinaface_mobilenet--736x1280_quant_hailort_hailo8l_1"

    # Initialize detector
    detector = RetinaFaceDetector(model_name, zoo_path)

    # Setup Picamera2 to capture directly in RGB888 (no color conversion needed)
    picam2 = Picamera2()
    preview_cfg = picam2.create_preview_configuration(
        main={"format": "RGB888", "size": (width, height)}
    )
    picam2.configure(preview_cfg)
    picam2.start()

    # Queue for frames
    frame_queue = Queue(maxsize=2)

    # Function to continuously capture frames in a separate thread
    def capture_frames():
        capture = picam2.capture_array
        while True:
            try:
                frame_rgb = capture()
                frame_queue.put(frame_rgb, timeout=0.5)
            except Exception as e:
                logging.error(f"Capture error: {e}")

    # Start capture thread
    Thread(target=capture_frames, daemon=True).start()

    logging.info(f"Starting FPS test at resolution {width}x{height}. Press CTRL+C to stop.")

    # Local function references for speed
    infer = detector.infer
    parse = detector.parse_detections

    frame_count = 0
    total_detections = 0
    start_time = time.time()

    try:
        while True:
            frame_rgb = frame_queue.get()  # Get latest frame
            outputs = infer(frame_rgb)
            num_faces = parse(outputs)

            frame_count += 1
            total_detections += num_faces

            elapsed = time.time() - start_time
            if elapsed >= 1.0:
                fps = frame_count / elapsed
                print(f"\rFPS: {fps:.2f} | Detections/sec: {total_detections}", end="")
                frame_count = 0
                total_detections = 0
                start_time = time.time()

    except KeyboardInterrupt:
        logging.info("\nFPS test stopped.")
    finally:
        picam2.stop()


if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="Optimized Hailo RetinaFace FPS test with detection count")
    parser.add_argument("--width", type=int, default=736, help="Image width (default: 736)")
    parser.add_argument("--height", type=int, default=1280, help="Image height (default: 1280)")
    args = parser.parse_args()

    run_fps_test(args.width, args.height)
