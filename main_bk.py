import cv2
import numpy as np
import time
import degirum
import logging
import sys

logging.basicConfig(level=logging.INFO, format='[%(levelname)s] %(message)s')


class RetinaFaceDetector:
    def __init__(self, model_path: str, input_size=(736, 1280)):
        self.input_height, self.input_width = input_size
        logging.info(f"Loading RetinaFace model from: {model_path}")
        # Use degirum.aiclient.AIModel to load the local .hef model
        self.pipeline = degirum.aiclient.AIModel(degirum.LOCAL, model_path)
        logging.info("Model loaded successfully.")

    def preprocess(self, frame: np.ndarray) -> np.ndarray:
        # Resize frame to model input size (WxH)
        resized = cv2.resize(frame, (self.input_width, self.input_height))
        # Convert BGR to RGB if needed (check model input format)
        # Here assuming model expects RGB
        rgb = cv2.cvtColor(resized, cv2.COLOR_BGR2RGB)
        # Normalize if required (some models expect [0,1] or mean-subtracted)
        # For quantized Hailo models, often no normalization needed, check your model docs
        return rgb

    def run_inference(self, frame: np.ndarray):
        # frame is preprocessed numpy array (H,W,C)
        # For degirum.aiclient.AIModel, pass the frame directly (no need for batch dimension)
        outputs = self.pipeline(frame)
        return outputs

    def parse_outputs(self, outputs):
        """
        Parse RetinaFace outputs into bounding boxes and confidence scores.
        The output format depends on the model.  
        For RetinaFace MobileNet on Hailo8L, outputs usually contain:
          - bounding boxes: [N,4] (x1,y1,x2,y2)
          - scores: [N]
        This parsing may need adjustment depending on actual output format.
        """
        # Dummy parser: adjust this part based on your model's actual output keys and shapes
        # For example:
        # outputs is a dict with keys like 'boxes' and 'scores'
        try:
            boxes = outputs['boxes']
            scores = outputs['scores']
        except Exception as e:
            logging.error(f"Error parsing outputs: {e}")
            return []

        # Filter boxes by confidence threshold
        conf_threshold = 0.5
        faces = []
        for box, score in zip(boxes, scores):
            if score >= conf_threshold:
                x1, y1, x2, y2 = box
                faces.append((int(x1), int(y1), int(x2), int(y2), float(score)))
        return faces


def main():
    model_path = "/home/<USER>/Downloads/retinaface_mobilenet--736x1280_quant_hailort_hailo8l_1/retinaface_mobilenet--736x1280_quant_hailort_hailo8l_1.hef"  # Update if different

    detector = RetinaFaceDetector(model_path)

    # Open Pi HQ camera (usually index 0)
    cap = cv2.VideoCapture(0)
    if not cap.isOpened():
        logging.error("Cannot open camera")
        sys.exit(1)

    prev_time = time.time()
    frame_count = 0

    logging.info("Starting video capture. Press 'q' to quit.")

    while True:
        ret, frame = cap.read()
        if not ret:
            logging.warning("Failed to grab frame")
            break

        input_frame = detector.preprocess(frame)
        outputs = detector.run_inference(input_frame)

        faces = detector.parse_outputs(outputs)

        # Draw bounding boxes and scores
        for (x1, y1, x2, y2, score) in faces:
            cv2.rectangle(frame, (x1, y1), (x2, y2), (0, 255, 0), 2)
            cv2.putText(frame, f"{score:.2f}", (x1, y1 - 10),
                        cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 255, 0), 2)

        # FPS calculation
        frame_count += 1
        if frame_count >= 10:
            current_time = time.time()
            fps = frame_count / (current_time - prev_time)
            prev_time = current_time
            frame_count = 0
        else:
            fps = None

        if fps:
            cv2.putText(frame, f"FPS: {fps:.2f}", (20, 30),
                        cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 255, 255), 2)

        cv2.imshow('RetinaFace Detection - Press q to quit', frame)

        if cv2.waitKey(1) & 0xFF == ord('q'):
            break

    cap.release()
    cv2.destroyAllWindows()
    logging.info("Exiting.")


if __name__ == "__main__":
    main()
