import cv2
import numpy as np
import time
import degirum
import logging
import sys

logging.basicConfig(level=logging.INFO, format='[%(levelname)s] %(message)s')


class RetinaFaceDetector:
    def __init__(self, model_path: str, input_size=(736, 1280)):
        self.input_height, self.input_width = input_size
        logging.info(f"Loading RetinaFace model from: {model_path}")
        self.pipeline = degirum.create_pipeline_from_file(model_path)
        self.pipeline.load()
        logging.info("Model loaded.")

    def preprocess(self, frame: np.ndarray) -> np.ndarray:
        # Resize frame to model input size (WxH)
        resized = cv2.resize(frame, (self.input_width, self.input_height))
        # Convert BGR to RGB (model expects RGB)
        rgb = cv2.cvtColor(resized, cv2.COLOR_BGR2RGB)
        # No normalization needed for quantized Hailo models (confirm with your model docs)
        return rgb

    def run_inference(self, frame: np.ndarray):
        # Add batch dimension NHWC uint8
        input_batch = np.expand_dims(frame, axis=0).astype(np.uint8)
        outputs = self.pipeline.run(input_batch)
        return outputs

    def parse_outputs(self, outputs):
        """
        Parse RetinaFace outputs into bounding boxes and confidence scores.

        NOTE: You need to adjust this based on your model output format.
        This is a dummy parser assuming outputs is a dict with 'boxes' and 'scores'.
        """

        try:
            boxes = outputs['boxes']
            scores = outputs['scores']
        except Exception as e:
            logging.error(f"Error parsing outputs: {e}")
            # For debugging, print raw outputs:
            logging.info(f"Raw outputs keys: {list(outputs.keys())}")
            return []

        conf_threshold = 0.5
        faces = []
        for box, score in zip(boxes, scores):
            if score >= conf_threshold:
                x1, y1, x2, y2 = box
                faces.append((int(x1), int(y1), int(x2), int(y2), float(score)))
        return faces


def main():
    model_path = "/home/<USER>/Downloads/retinaface_mobilenet--736x1280_quant_hailort_hailo8l_1/retinaface_mobilenet--736x1280_quant_hailort_hailo8l_1.hef"

    detector = RetinaFaceDetector(model_path)

    cap = cv2.VideoCapture(0)
    if not cap.isOpened():
        logging.error("Cannot open camera")
        sys.exit(1)

    prev_time = time.time()
    frame_count = 0

    logging.info("Starting video capture. Press 'q' to quit.")

    while True:
        ret, frame = cap.read()
        if not ret:
            logging.warning("Failed to grab frame")
            break

        input_frame = detector.preprocess(frame)
        outputs = detector.run_inference(input_frame)

        faces = detector.parse_outputs(outputs)

        # Draw bounding boxes and confidence
        for (x1, y1, x2, y2, score) in faces:
            cv2.rectangle(frame, (x1, y1), (x2, y2), (0, 255, 0), 2)
            cv2.putText(frame, f"{score:.2f}", (x1, y1 - 10),
                        cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 255, 0), 2)

        # FPS calculation every 10 frames
        frame_count += 1
        if frame_count >= 10:
            current_time = time.time()
            fps = frame_count / (current_time - prev_time)
            prev_time = current_time
            frame_count = 0
        else:
            fps = None

        if fps:
            cv2.putText(frame, f"FPS: {fps:.2f}", (20, 30),
                        cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 255, 255), 2)

        cv2.imshow('RetinaFace Detection - Press q to quit', frame)

        if cv2.waitKey(1) & 0xFF == ord('q'):
            break

    cap.release()
    cv2.destroyAllWindows()
    logging.info("Exiting.")


if __name__ == "__main__":
    main()
