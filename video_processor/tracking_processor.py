
# tracking_processor.py
"""
TrackingProcessor: integrates detection + tracker adapters (ByteTrack preferred, OpenCV fallback).

Fixes included:
 - If tracker returns no tracks but detections exist, draw detections so user sees boxes.
 - Handles tracker failures by recreating tracker and drawing raw detections as fallback.
"""
from __future__ import annotations

import logging
import time
from typing import Any, Dict, List, Optional

import numpy as np

from showcase_framework_final import FramePacket, FrameProcessor, Detection
from trackers import Track, get_tracker

logger = logging.getLogger("tracking_processor")
logging.basicConfig(level=logging.INFO, format="[%(levelname)s] %(message)s")


class TrackingProcessor(FrameProcessor):
    def __init__(
        self,
        conf_threshold: float = 0.3,
        tracker_name: str = "bytetrack",
        det_interval: int = 2,
        mode: str = "hybrid",
        allow_fallback_to_opencv: bool = True,
        tracker_kwargs: Optional[Dict] = None,
    ):
        super().__init__(conf_threshold=float(conf_threshold))
        self.tracker_name = tracker_name or "bytetrack"
        self.det_interval = max(1, int(det_interval))
        self.mode = mode
        self.allow_fallback_to_opencv = bool(allow_fallback_to_opencv)
        self.tracker_kwargs = tracker_kwargs or {}

        self._tracker = None
        self._frame_count = 0
        self._last_update_ts = 0.0

    def _ensure_tracker(self, frame: np.ndarray):
        if self._tracker is not None:
            return
        try:
            self._tracker = get_tracker(self.tracker_name, **self.tracker_kwargs)
            logger.info("Using tracker: %s", self._tracker.name())
        except Exception as e:
            logger.warning("Requested tracker '%s' failed to initialize: %s", self.tracker_name, e)
            if self.allow_fallback_to_opencv:
                logger.info("Falling back to OpenCV CSRT multi-tracker.")
                self._tracker = get_tracker("opencv_csrt")
            else:
                raise

    def process(self, pkt: FramePacket) -> np.ndarray:
        frame = pkt.frame_rgb
        now = pkt.timestamp or time.time()
        self._ensure_tracker(frame)

        # decide whether to run detection this frame
        run_detection = False
        if self.mode == "detection_only":
            run_detection = True
        elif self.mode == "tracking_only":
            # run detection only if no tracks exist yet
            try:
                has_tracks = bool(getattr(self._tracker, "_tracks", None))
                run_detection = not has_tracks
            except Exception:
                run_detection = False
        else:  # hybrid
            run_detection = (self._frame_count % self.det_interval) == 0

        detections_for_tracker: List[Dict[str, Any]] = []
        parsed_dets = []
        if run_detection:
            h, w = frame.shape[:2]
            dets = self.parse_detections(pkt.model_out, (h, w))
            parsed_dets = dets
            for d in dets:
                detections_for_tracker.append({"bbox": tuple(map(int, d.bbox)), "score": d.score, "label": d.label, "raw": d.raw})

        tracks: List[Track] = []
        try:
            tracks = self._tracker.update(detections_for_tracker, frame, now)
        except Exception as e:
            logger.exception("Tracker update failed: %s", e)
            # try re-init tracker and fallback to OpenCV if allowed
            try:
                self._tracker = None
                self._ensure_tracker(frame)
                tracks = self._tracker.update(detections_for_tracker, frame, now)
            except Exception as e2:
                logger.exception("Tracker re-init failed: %s", e2)
                tracks = []

        draw_dets: List[Detection] = []
        if tracks:
            for t in tracks:
                label = (t.label or "") + f" ID:{t.track_id}"
                det = Detection(bbox=t.bbox, score=t.score, label=label, raw=t.raw)
                draw_dets.append(det)
        else:
            # if no tracks but we have detections parsed this frame, draw detections (so boxes visible)
            if parsed_dets:
                for d in parsed_dets:
                    # add detection label
                    lab = (d.label or "obj")
                    det = Detection(bbox=d.bbox, score=d.score, label=lab, raw=d.raw)
                    draw_dets.append(det)

        out = self.draw_boxes(frame, draw_dets, label_field=True, score_field=False, thickness=2)
        self._frame_count += 1
        return out
