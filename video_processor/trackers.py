
# trackers.py
"""
Tracker adapters (ByteTrack adapter + OpenCV fallback) behind a BaseTracker ABC.

This updated version includes robust OpenCV tracker creation (handles cv2 legacy)
and ensures that detections are drawn even when trackers haven't produced tracks yet.
"""
from __future__ import annotations

import abc
import logging
import threading
from dataclasses import dataclass
from typing import Any, Dict, List, Optional, Tuple

import cv2
import numpy as np

logger = logging.getLogger("trackers")
logging.basicConfig(level=logging.INFO, format="[%(levelname)s] %(message)s")


@dataclass
class Track:
    track_id: int
    bbox: Tuple[int, int, int, int]  # x1,y1,x2,y2
    score: Optional[float] = None
    label: Optional[str] = None
    raw: Any = None


class BaseTracker(abc.ABC):
    @abc.abstractmethod
    def update(self, detections: List[Dict[str, Any]], frame: np.ndarray, timestamp: float) -> List[Track]:
        pass

    @abc.abstractmethod
    def reset(self) -> None:
        pass

    @abc.abstractmethod
    def name(self) -> str:
        pass


class ByteTrackAdapter(BaseTracker):
    """
    Minimal ByteTrack adapter placeholder. It attempts to import known wrappers.
    If none are available, the constructor will raise ImportError.
    """
    def __init__(self, track_buffer: int = 30, iou_threshold: float = 0.3, allow_fallback: bool = False):
        self._tracker = None
        self._initialized = False
        self._track_buffer = int(track_buffer)
        self._iou_threshold = float(iou_threshold)
        self._allow_fallback = bool(allow_fallback)
        tried = []
        # Try a couple of common wrappers; if none found, raise ImportError to let caller fallback.
        try:
            from yolox.tracker.bytetrack import BYTETracker  # type: ignore
            self._BYTETracker = BYTETracker
            self._tracker_impl = "yolox.bytetrack"
            logger.info("ByteTrack adapter: using yolox.bytetrack.BYTETracker")
        except Exception as e:
            tried.append(("yolox.bytetrack.BYTETracker", e))
            try:
                import bytetrack as _bt  # type: ignore
                self._bytetrack = _bt
                self._tracker_impl = "bytetrack_module"
                logger.info("ByteTrack adapter: using community 'bytetrack' module")
            except Exception as e2:
                tried.append(("bytetrack", e2))
                raise ImportError(
                    "No ByteTrack python wrapper found. Tried: " + ", ".join([t[0] for t in tried])
                )

        self._next_id = 1

    def name(self) -> str:
        return "bytetrack"

    def reset(self) -> None:
        self._tracker = None
        self._initialized = False
        self._next_id = 1

    def _init_tracker(self, frame: np.ndarray):
        if hasattr(self, "_BYTETracker"):
            try:
                # construct with common minimal args if supported
                self._tracker = self._BYTETracker(track_thresh=0.3, track_buffer=self._track_buffer)
                self._initialized = True
            except Exception as e:
                logger.warning("Failed creating BYTETracker with defaults: %s", e)
                self._tracker = None
        elif hasattr(self, "_bytetrack"):
            # community module: we will use it as-is expecting it exposes an 'update' method
            self._tracker = self._bytetrack
            self._initialized = True
        else:
            raise RuntimeError("No usable ByteTrack implementation found.")

    def update(self, detections: List[Dict[str, Any]], frame: np.ndarray, timestamp: float) -> List[Track]:
        if not self._initialized:
            self._init_tracker(frame)
        # Prepare dets Nx5 [x1,y1,x2,y2,score]
        if detections:
            dets_arr = np.array([[float(d["bbox"][0]), float(d["bbox"][1]), float(d["bbox"][2]), float(d["bbox"][3]), float(d.get("score", 1.0))] for d in detections], dtype=np.float32)
        else:
            dets_arr = np.zeros((0, 5), dtype=np.float32)

        tracks: List[Track] = []
        try:
            if hasattr(self._tracker, "update"):
                out = self._tracker.update(dets_arr, frame)
                # adapt common patterns
                for t in out:
                    try:
                        tid = int(getattr(t, "track_id", getattr(t, "id", -1)))
                        if hasattr(t, "tlbr"):
                            x1, y1, x2, y2 = t.tlbr
                        elif hasattr(t, "tlwh"):
                            x, y, w, h = t.tlwh
                            x1, y1, x2, y2 = x, y, x + w, y + h
                        elif isinstance(t, (list, tuple, np.ndarray)):
                            arr = np.asarray(t)
                            if arr.size >= 5:
                                # assume [id,x1,y1,x2,y2] or [x1,y1,x2,y2,id]
                                if arr.size >= 5 and arr[0] >= 1:
                                    tid = int(arr[0])
                                    x1, y1, x2, y2 = arr[1:5]
                                else:
                                    x1, y1, x2, y2, tid = arr[:5]
                        else:
                            continue
                        tracks.append(Track(track_id=tid, bbox=(int(x1), int(y1), int(x2), int(y2)), score=None, label=None, raw=t))
                    except Exception:
                        logger.debug("Failed to parse a track item: %s", t)
                return tracks
        except Exception as e:
            raise RuntimeError(f"ByteTrack adapter update failed: {e}")
        raise RuntimeError("ByteTrack implementation did not return tracks in a recognized format.")


def _iou(boxA, boxB) -> float:
    xA = max(boxA[0], boxB[0])
    yA = max(boxA[1], boxB[1])
    xB = min(boxA[2], boxB[2])
    yB = min(boxA[3], boxB[3])
    interW = max(0, xB - xA)
    interH = max(0, yB - yA)
    interArea = interW * interH
    boxAArea = max(0, boxA[2] - boxA[0]) * max(0, boxA[3] - boxA[1])
    boxBArea = max(0, boxB[2] - boxB[0]) * max(0, boxB[3] - boxB[1])
    unionArea = boxAArea + boxBArea - interArea
    if unionArea <= 0:
        return 0.0
    return interArea / unionArea


class OpenCVMultiTracker(BaseTracker):
    def __init__(self, tracker_type: str = "CSRT", max_age: int = 30, iou_threshold: float = 0.3):
        self.tracker_type = tracker_type.upper()
        self.max_age = int(max_age)
        self.iou_threshold = float(iou_threshold)
        self._tracks: Dict[int, Dict] = {}
        self._lock = threading.Lock()
        self._next_id = 1

    def name(self) -> str:
        return f"opencv_{self.tracker_type.lower()}"

    def reset(self) -> None:
        with self._lock:
            self._tracks = {}
            self._next_id = 1

    def _make_opencv_tracker(self):
        # Try multiple locations for tracker constructors to support different OpenCV builds.
        creators = []
        if self.tracker_type == "CSRT":
            creators = ["TrackerCSRT_create", "legacy.TrackerCSRT_create"]
        elif self.tracker_type == "KCF":
            creators = ["TrackerKCF_create", "legacy.TrackerKCF_create"]
        elif self.tracker_type == "MOSSE":
            creators = ["TrackerMOSSE_create", "legacy.TrackerMOSSE_create"]
        else:
            creators = ["TrackerCSRT_create", "legacy.TrackerCSRT_create"]

        for name in creators:
            try:
                parts = name.split(".")
                if len(parts) == 1:
                    ctor = getattr(cv2, parts[0], None)
                else:
                    # e.g., cv2.legacy.TrackerCSRT_create
                    mod = cv2.legacy if hasattr(cv2, "legacy") else None
                    if mod is not None:
                        ctor = getattr(mod, parts[1], None)
                    else:
                        ctor = None
                if callable(ctor):
                    return ctor()
            except Exception:
                continue
        # Last resort: try legacy module attribute access
        try:
            return cv2.TrackerCSRT_create()
        except Exception:
            raise RuntimeError("OpenCV CSRT tracker constructor not found; ensure opencv-contrib-python is installed.")

    def update(self, detections: List[Dict[str, Any]], frame: np.ndarray, timestamp: float) -> List[Track]:
        h, w = frame.shape[:2]
        with self._lock:
            # If detections empty -> advance trackers
            if not detections:
                to_delete = []
                for tid, rec in list(self._tracks.items()):
                    tracker = rec["tracker"]
                    try:
                        ok, bbox = tracker.update(cv2.cvtColor(frame, cv2.COLOR_RGB2BGR))
                    except Exception:
                        ok = False
                        bbox = None
                    if not ok or bbox is None:
                        rec["age"] = rec.get("age", 0) + 1
                        if rec["age"] > self.max_age:
                            to_delete.append(tid)
                        continue
                    x, y, bw, bh = bbox
                    x1, y1, x2, y2 = int(x), int(y), int(x + bw), int(y + bh)
                    rec["bbox"] = (x1, y1, x2, y2)
                    rec["age"] = 0
                    rec["last_seen_ts"] = timestamp
                for tid in to_delete:
                    del self._tracks[tid]
            else:
                det_bboxes = [tuple(map(int, d["bbox"])) for d in detections]
                # Build IOU matrix
                track_items = list(self._tracks.items())
                iou_matrix = []
                for tid, rec in track_items:
                    row = [_iou(rec["bbox"], db) for db in det_bboxes]
                    iou_matrix.append(row)
                matched_det_idx = set()
                if iou_matrix:
                    mat = np.array(iou_matrix)
                    # greedy matching
                    while True:
                        r, c = divmod(int(mat.argmax()), mat.shape[1])
                        best = float(mat[r, c])
                        if best <= self.iou_threshold:
                            break
                        tid = track_items[r][0]
                        det_idx = c
                        matched_det_idx.add(det_idx)
                        # reinit tracker with detection bbox
                        db = det_bboxes[det_idx]
                        try:
                            tr = self._make_opencv_tracker()
                            x1, y1, x2, y2 = db
                            bbox_xywh = (x1, y1, x2 - x1, y2 - y1)
                            tr.init(cv2.cvtColor(frame, cv2.COLOR_RGB2BGR), bbox_xywh)
                            self._tracks[tid]["tracker"] = tr
                            self._tracks[tid]["bbox"] = db
                            self._tracks[tid]["age"] = 0
                            self._tracks[tid]["last_seen_ts"] = timestamp
                        except Exception as e:
                            logger.debug("Failed to reinit tracker %s: %s", tid, e)
                        mat[r, :] = -1.0
                        mat[:, c] = -1.0

                # create new tracks for unmatched detections
                for i, db in enumerate(det_bboxes):
                    if i in matched_det_idx:
                        continue
                    x1, y1, x2, y2 = db
                    try:
                        tr = self._make_opencv_tracker()
                        bbox_xywh = (x1, y1, x2 - x1, y2 - y1)
                        tr.init(cv2.cvtColor(frame, cv2.COLOR_RGB2BGR), bbox_xywh)
                        tid = self._next_id
                        self._next_id += 1
                        self._tracks[tid] = {
                            "tracker": tr,
                            "bbox": db,
                            "age": 0,
                            "label": detections[i].get("label"),
                            "score": detections[i].get("score"),
                            "last_seen_ts": timestamp,
                        }
                    except Exception as e:
                        logger.debug("Failed to init new tracker: %s", e)

                # cleanup
                to_delete = [tid for tid, rec in self._tracks.items() if rec.get("age", 0) > self.max_age]
                for tid in to_delete:
                    del self._tracks[tid]

            # Build return list
            out = []
            for tid, rec in list(self._tracks.items()):
                bbox = rec.get("bbox", (0, 0, 0, 0))
                out.append(Track(track_id=tid, bbox=bbox, score=rec.get("score"), label=rec.get("label"), raw=rec))
            return out


def get_tracker(name: str = "bytetrack", **kwargs) -> BaseTracker:
    name = (name or "").lower()
    if name.startswith("byt") or name.startswith("byte"):
        return ByteTrackAdapter(**kwargs)
    if name.startswith("opencv") or name in ("csrt", "kcf", "mosse"):
        typ = "CSRT"
        if "kcf" in name:
            typ = "KCF"
        if "mosse" in name:
            typ = "MOSSE"
        return OpenCVMultiTracker(tracker_type=typ, **kwargs)
    return OpenCVMultiTracker(tracker_type="CSRT", **kwargs)
