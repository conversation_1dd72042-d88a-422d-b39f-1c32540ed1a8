import cv2
import time

# Use libcamera's preview stream via GStreamer pipeline
gst_pipeline = (
    "libcamerasrc ! "
    "video/x-raw,width=640,height=480,format=RGB ! "
    "videoconvert ! appsink"
)

cap = cv2.VideoCapture(gst_pipeline, cv2.CAP_GSTREAMER)

if not cap.isOpened():
    print("Error: Could not open camera with GStreamer pipeline.")
    exit(1)

print("Camera started. Capturing 5 frames...")
for i in range(5):
    ret, frame = cap.read()
    if not ret:
        print(f"Failed to capture frame {i+1}")
        break
    print(f"Captured frame {i+1}, shape: {frame.shape}")
    time.sleep(1)

cap.release()
print("Camera stopped.")
