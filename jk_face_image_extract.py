#!/usr/bin/env python3
"""
jk_face_image_extract_debug_fixed.py

Debug-friendly collector:
- captures frames from Picamera2
- runs DeGirum RetinaFace
- prints per-frame detection summary in --debug mode
- saves aligned 112x112 images when detection present (robust parsing)
Usage:
  python jk_face_image_extract_debug_fixed.py --root_folder ~/faces --zoo_path ~/degirum-zoo --duration 30 --count 10 --debug --width 1280 --height 720
"""

import os
import time
import argparse
import logging
import math
import numpy as np
import cv2
import degirum as dg
from picamera2 import Picamera2
from PIL import Image

logging.basicConfig(level=logging.INFO, format="[%(levelname)s] %(message)s")

REF_KPS_112 = np.array([
    [38.2946, 51.6963],
    [73.5318, 51.5014],
    [56.0252, 71.7366],
    [41.5493, 92.3655],
    [70.7299, 92.2041]
], dtype=np.float32)


def save_aligned_rgb_image(path: str, aligned_rgb: np.ndarray, debug_log: bool = False):
    """
    aligned_rgb: HxWx3 uint8 RGB image (NOT BGR).
    Saves with PIL (correct encoding) after a heuristic channel-order check.
    """
    if aligned_rgb is None:
        raise ValueError("aligned_rgb is None")

    img = aligned_rgb
    if img.dtype != np.uint8:
        img = np.clip(img, 0, 255).astype(np.uint8)

    if img.ndim != 3 or img.shape[2] < 3:
        raise ValueError(f"Unexpected aligned image shape: {img.shape}")

    # compute mean per channel in the central region (to focus on face)
    h, w = img.shape[:2]
    cx1 = max(0, w//2 - w//6); cx2 = min(w, w//2 + w//6)
    cy1 = max(0, h//2 - h//6); cy2 = min(h, h//2 + h//6)
    region = img[cy1:cy2, cx1:cx2]
    means = region.mean(axis=(0,1))  # [R_mean, G_mean, B_mean]
    Rm, Gm, Bm = float(means[0]), float(means[1]), float(means[2])

    # Heuristic check: skin tones & natural faces typically have R >= G >= B (often R > B).
    swapped = False
    if Rm + 5 < Bm:  # blue significantly higher than red → swap
        if debug_log:
            logging.info("Color heuristic: detected B > R in central region (R=%.1f G=%.1f B=%.1f) — swapping channels.", Rm, Gm, Bm)
        img = img[..., ::-1].copy()
        swapped = True
        # recompute means after swap for logs
        region = img[cy1:cy2, cx1:cx2]
        means = region.mean(axis=(0,1))
        Rm, Gm, Bm = float(means[0]), float(means[1]), float(means[2])

    # Save with PIL (ensures proper sRGB JPEG from RGB array)
    try:
        pil = Image.fromarray(img, mode="RGB")
        pil.save(path, format="JPEG", quality=95)
    except Exception as e:
        logging.warning("PIL save failed (%s). Falling back to cv2.imwrite.", e)
        try:
            cv2.imwrite(path, cv2.cvtColor(img, cv2.COLOR_RGB2BGR))
        except Exception as e2:
            logging.exception("cv2.imwrite fallback failed: %s", e2)
            raise

    logging.info("Saved image: %s (R=%.1f G=%.1f B=%.1f swapped=%s)", path, Rm, Gm, Bm, swapped)


class RetinaFaceDetector:
    def __init__(self, model_name: str, zoo_path: str):
        zoo_path = os.path.expanduser(zoo_path) if zoo_path else None
        logging.info("Connecting to DeGirum local zoo: %s", zoo_path)
        self.zoo = dg.connect(dg.LOCAL, zoo_path)
        self.model = self.zoo.load_model(model_name)
        # Ensure model expects RGB (Picamera2 will give RGB888)
        try:
            self.model.input_numpy_colorspace = "RGB"
        except Exception:
            pass

        # Get model input size from model name (format: model--WIDTHxHEIGHT_...)
        self.model_width = 736  # default
        self.model_height = 1280  # default
        try:
            parts = model_name.split('--')[1].split('_')[0]  # get "736x1280" part
            w, h = parts.split('x')
            self.model_width = int(w)
            self.model_height = int(h)
        except Exception:
            logging.warning("Could not parse model input size from name, using defaults: %dx%d",
                          self.model_width, self.model_height)

        logging.info("Detector model ready. Expected input size: %dx%d", self.model_width, self.model_height)

    def detect(self, frame_rgb):
        # Resize frame to match model expected input size
        h, w = frame_rgb.shape[:2]
        if w != self.model_width or h != self.model_height:
            # Resize maintaining aspect ratio and pad if needed
            resized = cv2.resize(frame_rgb, (self.model_width, self.model_height), interpolation=cv2.INTER_LINEAR)
            return self.model.predict(resized), (w / self.model_width, h / self.model_height)
        else:
            return self.model.predict(frame_rgb), (1.0, 1.0)


def safe_parse_bbox(raw_bbox, scale_x=1.0, scale_y=1.0):
    try:
        arr = np.array(raw_bbox, dtype=float).flatten()
    except Exception:
        flat = []
        for v in raw_bbox:
            for x in np.asarray(v).flatten():
                flat.append(float(x))
        arr = np.array(flat, dtype=float)
    if arr.size != 4:
        raise ValueError("bbox must contain 4 values")
    x1, y1, x2, y2 = arr.tolist()
    if (x2 - x1) <= 1.0:
        x2 = x1 + x2
    if (y2 - y1) <= 1.0:
        y2 = y1 + y2
    # Apply scaling to convert from model coordinates to original frame coordinates
    x1 *= scale_x
    y1 *= scale_y
    x2 *= scale_x
    y2 *= scale_y
    return [int(round(x1)), int(round(y1)), int(round(x2)), int(round(y2))]


def safe_parse_landmarks(raw_landmarks, w, h, scale_x=1.0, scale_y=1.0):
    if raw_landmarks is None:
        raise ValueError("No landmarks")
    try:
        arr = np.asarray(raw_landmarks, dtype=float).flatten()
    except Exception:
        flat = []
        for item in raw_landmarks:
            sub = np.asarray(item).flatten()
            for v in sub:
                flat.append(float(v))
        arr = np.array(flat, dtype=float)
    if arr.size == 10:
        arr = arr.reshape(5, 2)
    else:
        try:
            arr = arr.reshape(5, 2)
        except Exception:
            raise ValueError(f"Unsupported landmark shape: {arr.shape}")
    if arr.max() <= 1.01:
        arr[:, 0] *= w
        arr[:, 1] *= h
    # Apply scaling to convert from model coordinates to original frame coordinates
    arr[:, 0] *= scale_x
    arr[:, 1] *= scale_y
    return arr.astype(np.float32)


def align_face_to_112(frame_rgb, landmarks=None, bbox=None):
    H, W = frame_rgb.shape[:2]
    if landmarks is not None:
        try:
            src = np.array(landmarks, dtype=np.float32)
            M, _ = cv2.estimateAffinePartial2D(src, REF_KPS_112, method=cv2.RANSAC, ransacReprojThreshold=5.0)
            if M is not None:
                aligned = cv2.warpAffine(frame_rgb, M, (112, 112), flags=cv2.INTER_LINEAR, borderMode=cv2.BORDER_REPLICATE)
                return aligned
        except Exception:
            pass
    if bbox is not None:
        x1, y1, x2, y2 = bbox
        x1 = max(0, x1); y1 = max(0, y1); x2 = min(W - 1, x2); y2 = min(H - 1, y2)
        if x2 > x1 and y2 > y1:
            # Increase margin to 40% to capture more of the face and avoid cutoff
            margin = int(round(0.4 * max(x2 - x1, y2 - y1)))
            cx1 = max(0, x1 - margin); cy1 = max(0, y1 - margin)
            cx2 = min(W - 1, x2 + margin); cy2 = min(H - 1, y2 + margin)
            crop = frame_rgb[cy1:cy2, cx1:cx2]
            if crop.size != 0:
                return cv2.resize(crop, (112, 112), interpolation=cv2.INTER_LINEAR)
    # Fallback: center crop with larger area
    hmin = min(H, W); cy = H // 2; cx = W // 2; half = int(hmin * 0.6) // 2  # Increased from 0.5 to 0.6
    crop = frame_rgb[max(0, cy - half):min(H, cy + half), max(0, cx - half):min(W, cx + half)]
    if crop.size == 0:
        return cv2.resize(frame_rgb, (112, 112), interpolation=cv2.INTER_LINEAR)
    return cv2.resize(crop, (112, 112), interpolation=cv2.INTER_LINEAR)


def collect_debug(root_folder, zoo_path, duration_s, target_count,
                  score_thresh=0.05, force_attempts=60, debug=False, width=1280, height=720, person_name=None):
    if person_name is None:
        person_name = input("Enter person's name (folder will be created): ").strip()
    if not person_name:
        logging.error("Empty name. Exiting.")
        return
    folder = os.path.join(os.path.expanduser(root_folder), person_name)
    os.makedirs(folder, exist_ok=True)
    logging.info("Saving to: %s", folder)

    detector = RetinaFaceDetector("retinaface_mobilenet--736x1280_quant_hailort_hailo8l_1", zoo_path)

    picam2 = Picamera2()
    # Use model's expected resolution for better compatibility
    model_width, model_height = detector.model_width, detector.model_height
    cfg = picam2.create_preview_configuration(main={"format": "RGB888", "size": (model_width, model_height)})
    picam2.configure(cfg)
    picam2.start()
    logging.info("Camera started with resolution %dx%d (model expects %dx%d).",
                 model_width, model_height, detector.model_width, detector.model_height)

    # Warm up the model with a dummy frame to avoid long first detection
    logging.info("Warming up detection model...")
    try:
        dummy_frame = picam2.capture_array("main")
        h, w = dummy_frame.shape[:2]
        if w != detector.model_width or h != detector.model_height:
            dummy_frame = cv2.resize(dummy_frame, (detector.model_width, detector.model_height), interpolation=cv2.INTER_LINEAR)
        _ = detector.model.predict(dummy_frame)
        logging.info("Model warmup completed.")
    except Exception as e:
        logging.warning("Model warmup failed: %s", e)

    # Try to enable AWB so libcamera can stabilise color
    try:
        picam2.set_controls({"AwbEnable": True})
        logging.info("Requested AWB enable via set_controls().")
    except Exception:
        logging.debug("Could not set AwbEnable via set_controls(); continuing with defaults.")
    time.sleep(1.0)  # let AWB stabilize and camera warm up

    saved = 0
    start = time.time()
    attempts_since_last_save = 0
    frame_idx = 0

    try:
        logging.info("Starting capture loop for %d seconds, target: %d images", duration_s, target_count)
        loop_count = 0
        while time.time() - start < duration_s and saved < target_count:
            loop_count += 1
            frame_idx += 1
            elapsed = time.time() - start
            if debug and (frame_idx <= 5 or frame_idx % 20 == 0):
                logging.info("Loop iteration %d, elapsed: %.1fs, saved: %d, time_left: %.1fs",
                           frame_idx, elapsed, saved, duration_s - elapsed)

            # capture robustly
            frame = None
            try:
                frame = picam2.capture_array("main")
                if debug and frame_idx <= 5:
                    logging.info("Frame %d captured successfully: shape=%s", frame_idx, frame.shape if frame is not None else "None")
            except Exception as e:
                logging.warning("capture_array('main') failed: %s", e)
                try:
                    frame = picam2.capture_array()
                    if debug and frame_idx <= 5:
                        logging.info("Frame %d captured with fallback: shape=%s", frame_idx, frame.shape if frame is not None else "None")
                except Exception as e2:
                    logging.warning("capture_array() fallback failed: %s", e2)
                    time.sleep(0.15)
                    continue
            if frame is None:
                if debug:
                    logging.warning("Frame is None at iteration %d", frame_idx)
                time.sleep(0.05)
                continue

            h, w = frame.shape[:2]
            if debug and frame_idx <= 5:
                logging.info("Processing frame %d: shape=%s, model expects %dx%d",
                           frame_idx, (h, w), detector.model_width, detector.model_height)

            # Save debug frames for first few iterations (optional)
            if debug and frame_idx <= 2 and saved == 0:  # Only save first 2 frames if no faces found yet
                debug_path = os.path.join(folder, f"debug_frame_{frame_idx}.jpg")
                cv2.imwrite(debug_path, cv2.cvtColor(frame, cv2.COLOR_RGB2BGR))
                logging.info("Saved debug frame: %s", debug_path)

            # Check if frame needs to be resized to match model input
            if w != detector.model_width or h != detector.model_height:
                if debug and frame_idx <= 5:
                    logging.info("Resizing frame from %dx%d to %dx%d", w, h, detector.model_width, detector.model_height)
                frame_resized = cv2.resize(frame, (detector.model_width, detector.model_height), interpolation=cv2.INTER_LINEAR)
                scale_x = w / detector.model_width
                scale_y = h / detector.model_height
            else:
                frame_resized = frame
                scale_x, scale_y = 1.0, 1.0

            # detect
            try:
                out = detector.model.predict(frame_resized)
            except Exception as e:
                logging.error("Detector exception on frame %d: %s", frame_idx, e, exc_info=True)
                time.sleep(0.1)
                continue

            results = getattr(out, "results", None)
            det_count = len(results) if results else 0

            # debug print (throttled)
            if debug and (frame_idx <= 5 or frame_idx % 5 == 0 or det_count > 0):
                logging.info("Frame %d: detections=%d, frame_shape=%s", frame_idx, det_count, frame.shape)
                if det_count > 0:
                    first = results[0]
                    try:
                        keys = list(first.keys())
                    except Exception:
                        keys = []
                    logging.info("  keys=%s", keys)
                    top_score = float(max((r.get("score", 0.0) for r in results), default=0.0))
                    logging.info("  top_score=%.4f", top_score)
                else:
                    logging.info("  No detections found")

            if not results:
                attempts_since_last_save += 1
                if debug and frame_idx <= 10:
                    logging.info("Frame %d: No detections found, continuing loop", frame_idx)
                time.sleep(0.03)
                continue

            # choose best detection by score
            try:
                best = max(results, key=lambda r: float(r.get("score", 0.0)))
            except Exception:
                best = results[0]
            score = float(best.get("score", 0.0))

            if debug:
                logging.info("  best score=%.4f", score)

            if score < score_thresh:
                attempts_since_last_save += 1
                time.sleep(0.03)
                continue

            # robust parse bbox/landmarks
            raw_bbox = best.get("bbox") or best.get("box") or best.get("rectangle")
            raw_lm = best.get("landmarks") or best.get("landmark") or best.get("keypoints")

            bbox = None
            lm = None
            if raw_bbox is not None:
                try:
                    bbox = safe_parse_bbox(raw_bbox, scale_x, scale_y)
                except Exception as e:
                    if debug:
                        logging.debug("bbox parse failed: %s", e)
                    bbox = None
            if raw_lm is not None:
                try:
                    lm = safe_parse_landmarks(raw_lm, w, h, scale_x, scale_y)
                except Exception as e:
                    if debug:
                        logging.debug("landmark parse failed: %s", e)
                    lm = None

            # get aligned face (prefer landmarks)
            aligned = align_face_to_112(frame, landmarks=lm, bbox=bbox)

            # TRY to save; fallback to raw crop if aligned is invalid
            if aligned is None:
                logging.warning("Aligned is None (unexpected). Skipping save.")
                attempts_since_last_save += 1
                continue

            ts = int(time.time() * 1000)
            fname = f"{person_name}_{saved+1}_{ts}.jpg"
            path = os.path.join(folder, fname)
            try:
                save_aligned_rgb_image(path, aligned, debug_log=True)
                saved += 1
                attempts_since_last_save = 0
                logging.info("Saved %d/%d: %s (score=%.3f) size=%s", saved, target_count, path, score, aligned.shape[:2])
            except Exception as e:
                logging.warning("Failed to write image: %s", e)
                attempts_since_last_save += 1

            # short pause to let user change pose
            time.sleep(0.3)

            # force save fallback to avoid infinite loop
            if attempts_since_last_save >= force_attempts:
                fname = f"{person_name}_{saved+1}_force_{ts}.jpg"
                path = os.path.join(folder, fname)
                try:
                    save_aligned_rgb_image(path, aligned, debug_log=True)
                    saved += 1
                    attempts_since_last_save = 0
                    logging.info("Force-saved %d/%d: %s", saved, target_count, path)
                except Exception as e:
                    logging.warning("Force save failed: %s", e)
                    attempts_since_last_save = 0

    except KeyboardInterrupt:
        logging.info("Interrupted")
    except Exception as e:
        logging.error("Unexpected error in main loop: %s", e, exc_info=True)

    finally:
        try:
            picam2.stop()
        except Exception:
            pass

    logging.info("Done. Saved %d images to %s (processed %d loop iterations)", saved, folder, loop_count)


if __name__ == "__main__":
    p = argparse.ArgumentParser()
    p.add_argument("--root_folder", default="faces")
    p.add_argument("--zoo_path", default="~/degirum-zoo")
    p.add_argument("--duration", type=int, default=60)
    p.add_argument("--count", type=int, default=10)
    p.add_argument("--score_thresh", type=float, default=0.05)
    p.add_argument("--force_attempts", type=int, default=60)
    p.add_argument("--width", type=int, default=1280, help="Camera width resolution (default: 1280 for 720p)")
    p.add_argument("--height", type=int, default=736, help="Camera height resolution (default: 720 for 720p)")
    p.add_argument("--person_name", type=str, help="Person name (skip interactive input)")
    p.add_argument("--debug", action="store_true", help="print per-frame debug info")
    args = p.parse_args()

    collect_debug(root_folder=args.root_folder, zoo_path=args.zoo_path,
                  duration_s=args.duration, target_count=args.count,
                  score_thresh=args.score_thresh, force_attempts=args.force_attempts,
                  debug=args.debug, width=args.width, height=args.height, person_name=args.person_name)
    # python jk_face_image_extract.py --width 1920 --height 1080 --root_folder faces --zoo_path ~/degirum-zoo --duration 20 --count 20
