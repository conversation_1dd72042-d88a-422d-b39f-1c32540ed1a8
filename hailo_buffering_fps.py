#!/usr/bin/env python3
"""
Buffered camera -> Hailo inference throughput tester for Raspberry Pi (Picamera2 + DeGirum)

Features:
- Capture frames from Picamera2 into an in-memory circular buffer (adjustable size)
- Separate inference worker that pulls frames from the buffer as fast as possible
- Options to overwrite oldest frames when buffer is full (drop_oldest) or drop newest
- Metrics printed periodically: capture FPS, inference FPS, buffer occupancy
- Also includes a dummy "blank frame" pure-inference benchmark mode

Notes / cautions:
- Storing many frames in memory can consume a lot of RAM. A single 640x640 RGB uint8 frame ~= 1.2 MB.
- Default behavior (drop_oldest=True) favors keeping capture running at max speed and gives model the most recent frames.
- Degirum model.predict() may release the GIL but if you run into thread-safety problems, use a single inference worker.

Usage examples:
  # Run buffered camera inference with 8-frame buffer, overwrite oldest when full (recommended for throughput)
  python3 rpi_hailo_buffered_inference.py --width 640 --height 640 --buffer-size 8 --drop-oldest

  # Dummy blank-frame pure inference benchmark (like your "blank image fps" script)
  python3 rpi_hailo_buffered_inference.py --dummy --duration 5 --model yolov8n_relu6_car--640x640_quant_hailort_multidevice_1

"""

import argparse
import logging
import os
import signal
import sys
import threading
import time
from collections import deque

import degirum as dg
import numpy as np
from picamera2 import Picamera2
from libcamera import Transform


logging.basicConfig(level=logging.INFO, format="[%(levelname)s] %(message)s")
LOG = logging.getLogger("buffered_infer")


class ObjectDetector:
    def __init__(self, model_name: str, zoo_path: str):
        LOG.info(f"Connecting to DeGirum in local mode — zoo: {zoo_path}")
        self.zoo = dg.connect(dg.LOCAL, zoo_path)
        LOG.info(f"Loading model: {model_name}")
        self.model = self.zoo.load_model(model_name)
        LOG.info("Model loaded successfully.")

    def predict(self, frame_rgb: np.ndarray):
        return self.model.predict(frame_rgb)

    @staticmethod
    def parse_detections(outputs):
        try:
            return len(outputs.results)
        except Exception:
            return 0


class BufferedCamera:
    def __init__(self, width, height, buffer_size=8, drop_oldest=True, picam2_config_main=None):
        self.width = width
        self.height = height
        self.buffer_size = int(buffer_size)
        self.drop_oldest = bool(drop_oldest)

        self._buffer = deque(maxlen=self.buffer_size)
        self._lock = threading.Lock()
        self._stop_event = threading.Event()

        self.picam2 = Picamera2()
        config = self.picam2.create_video_configuration(
            main=picam2_config_main or {"size": (2028, 1520)},
            lores={"size": (width, height), "format": "RGB888"},
            transform=Transform(vflip=0, hflip=0),
            buffer_count=4,
        )
        self.picam2.configure(config)

        # metrics
        self.capture_count = 0
        self.last_capture_ts = time.time()

    def start(self):
        LOG.info("Starting Picamera2 capture...")
        self.picam2.start()
        self._capture_thread = threading.Thread(target=self._capture_loop, daemon=True)
        self._capture_thread.start()

    def stop(self):
        LOG.info("Stopping BufferedCamera...")
        self._stop_event.set()
        if hasattr(self, "_capture_thread"):
            self._capture_thread.join(timeout=2.0)
        try:
            self.picam2.stop()
        except Exception:
            pass

    def _capture_loop(self):
        while not self._stop_event.is_set():
            try:
                frame = self.picam2.capture_array("lores")
            except Exception as e:
                LOG.error(f"Error capturing frame: {e}")
                time.sleep(0.01)
                continue

            # store a contiguous copy to avoid referencing picamera buffers
            frame_copy = np.ascontiguousarray(frame)

            with self._lock:
                if len(self._buffer) >= self.buffer_size:
                    if self.drop_oldest:
                        # deque with maxlen will automatically discard leftmost
                        # but we want to explicitly pop left to keep counts consistent
                        try:
                            self._buffer.popleft()
                        except IndexError:
                            pass
                        self._buffer.append(frame_copy)
                    else:
                        # drop newest (do not append)
                        pass
                else:
                    self._buffer.append(frame_copy)

            self.capture_count += 1

    def get_frame(self):
        with self._lock:
            if len(self._buffer) == 0:
                return None
            return self._buffer.popleft()

    def buffer_occupancy(self):
        with self._lock:
            return len(self._buffer)


def run_buffered_inference(args):
    zoo_path = os.path.expanduser(args.zoo_path)
    detector = ObjectDetector(args.model, zoo_path)

    cam = BufferedCamera(args.width, args.height, buffer_size=args.buffer_size, drop_oldest=args.drop_oldest)
    cam.start()

    stop_evt = threading.Event()

    def signal_handler(signum, frame):
        LOG.info("Signal received, shutting down...")
        stop_evt.set()

    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)

    infer_count = 0
    infer_start = time.time()
    capture_count_snapshot = 0

    LOG.info(f"Started buffered inference. Buffer size: {args.buffer_size} | drop_oldest: {args.drop_oldest}")

    try:
        while not stop_evt.is_set():
            frame = cam.get_frame()
            if frame is None:
                # small sleep to yield CPU when buffer empty
                time.sleep(0.001)
                continue

            # run inference
            outputs = detector.predict(frame)
            _ = detector.parse_detections(outputs)

            infer_count += 1

            # print periodic stats
            now = time.time()
            if now - infer_start >= args.stats_interval:
                elapsed = now - infer_start
                infer_fps = infer_count / elapsed if elapsed > 0 else 0.0

                # capture FPS since last report
                current_capture_count = cam.capture_count
                capture_fps = (current_capture_count - capture_count_snapshot) / elapsed
                capture_count_snapshot = current_capture_count

                occupancy = cam.buffer_occupancy()

                print(f"\rCapture FPS: {capture_fps:.2f} | Infer FPS: {infer_fps:.2f} | Buffer occupancy: {occupancy}/{args.buffer_size}", end="")

                # reset
                infer_count = 0
                infer_start = now

    except KeyboardInterrupt:
        stop_evt.set()
    finally:
        LOG.info("Shutting down...")
        cam.stop()


def run_dummy_benchmark(args):
    zoo_path = os.path.expanduser(args.zoo_path)
    detector = ObjectDetector(args.model, zoo_path)

    dummy = np.zeros((args.height, args.width, 3), dtype=np.uint8)

    LOG.info(f"Running dummy inference benchmark for {args.duration}s")
    start = time.time()
    count = 0
    try:
        while time.time() - start < args.duration:
            detector.predict(dummy)
            count += 1
    except KeyboardInterrupt:
        pass

    fps = count / max(1e-9, args.duration)
    print(f"Pure inference FPS (dummy frame): {fps:.2f}")


def parse_args():
    p = argparse.ArgumentParser(description="Buffered Picamera2 -> Hailo inference throughput tester")
    p.add_argument("--width", type=int, default=640, help="Inference width")
    p.add_argument("--height", type=int, default=640, help="Inference height")
    p.add_argument("--model", type=str, default="yolov8n_relu6_car--640x640_quant_hailort_multidevice_1", help="Model name from degirum-zoo")
    p.add_argument("--zoo-path", type=str, default="~/degirum-zoo", help="Path to degirum-zoo")

    # buffering params
    p.add_argument("--buffer-size", type=int, default=8, help="Number of frames to keep in memory (circular buffer)")
    p.add_argument("--drop-oldest", action="store_true", help="When buffer is full, overwrite oldest frames (recommended for throughput)")
    p.add_argument("--drop-newest", dest="drop_oldest", action="store_false", help="When buffer is full, drop newest frame instead (keeps older frames)")

    p.add_argument("--stats-interval", type=float, default=1.0, help="Seconds between printing stats")

    # dummy benchmark
    p.add_argument("--dummy", action="store_true", help="Run dummy blank-frame pure-inference benchmark and exit")
    p.add_argument("--duration", type=float, default=5.0, help="Duration for dummy benchmark (seconds)")

    return p.parse_args()


if __name__ == "__main__":
    args = parse_args()

    if args.dummy:
        run_dummy_benchmark(args)
        sys.exit(0)

    try:
        run_buffered_inference(args)
    except Exception as e:
        LOG.exception(f"Fatal error: {e}")
        sys.exit(1)
