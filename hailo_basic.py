import os
import cv2
import time
import logging
import degirum as dg
from picamera2 import Picamera2
from libcamera import Transform

logging.basicConfig(level=logging.INFO, format='[%(levelname)s] %(message)s')

class RetinaFaceDetector:
    def __init__(self, model_name: str, zoo_path: str):
        logging.info(f"Connecting to DeGirum in local mode — zoo: {zoo_path}")
        self.zoo = dg.connect("localhost")  # Faster than dg.LOCAL with zoo_path
        logging.info(f"Loading model: {model_name}")
        self.model = self.zoo.load_model(model_name)
        logging.info("Model loaded successfully.")
        self._logged_results_example = False

    def infer(self, frame_rgb):
        return self.model.predict(frame_rgb)

    def parse_outputs(self, outputs, image_width, image_height):
        faces = []
        for det in outputs.results:
            try:
                if not self._logged_results_example:
                    logging.debug("Example detection dict: %s", det)
                    self._logged_results_example = True

                bbox = det.get("bbox", None)
                score = det.get("score", None) or det.get("confidence", None)
                if bbox is None or score is None:
                    continue
                if not (isinstance(bbox, (list, tuple)) and len(bbox) == 4):
                    continue

                x0, y0, x1, y1 = bbox

                if max(x0, y0, x1, y1) <= 1.01:  # normalized coords
                    x0 *= image_width
                    x1 *= image_width
                    y0 *= image_height
                    y1 *= image_height

                w_candidate = x1
                h_candidate = y1
                if (w_candidate > 0 and h_candidate > 0 and
                    (x0 + w_candidate) <= image_width + 1 and
                    (y0 + h_candidate) <= image_height + 1 and
                    (w_candidate < image_width and h_candidate < image_height) and
                    not (x1 > image_width * 0.9 and y1 > image_height * 0.9)):
                    x1 = x0 + w_candidate
                    y1 = y0 + h_candidate

                x1 = max(0, min(image_width - 1, int(round(x1))))
                x0 = max(0, min(image_width - 1, int(round(x0))))
                y1 = max(0, min(image_height - 1, int(round(y1))))
                y0 = max(0, min(image_height - 1, int(round(y0))))

                x_min, x_max = sorted((x0, x1))
                y_min, y_max = sorted((y0, y1))

                faces.append((x_min, y_min, x_max, y_max, float(score)))
            except Exception as e:
                logging.error("Error parsing a detection dict: %s", e)
        return faces

def main():
    zoo_path = os.path.expanduser("~/degirum-zoo")
    model_name = "retinaface_mobilenet--736x1280_quant_hailort_hailo8l_1"
    detector = RetinaFaceDetector(model_name, zoo_path)

    # Configure Picamera2 for HQ + LoRes streams
    picam2 = Picamera2()
    lores_size = (640, 480)  # Fast inference size
    config = picam2.create_video_configuration(
        main={"size": (2028, 1520)},  # HQ cam for display
        lores={"size": lores_size, "format": "RGB888"},
        transform=Transform(vflip=0, hflip=0),
        buffer_count=3
    )
    picam2.configure(config)
    picam2.start()

    cv2.startWindowThread()
    cv2.namedWindow('RetinaFace Detection', cv2.WINDOW_NORMAL)
    cv2.resizeWindow('RetinaFace Detection', 800, 600)

    frame_count = 0
    prev_time = time.time()

    logging.info("Starting video capture. Press 'q' to quit.")

    try:
        while True:
            # Get low-res RGB frame for inference
            frame_rgb = picam2.capture_array("lores")

            outputs = detector.infer(frame_rgb)
            img_h, img_w = frame_rgb.shape[:2]
            faces = detector.parse_outputs(outputs, img_w, img_h)

            # Get high-res frame for display
            frame_disp = picam2.capture_array("main")
            frame_disp = cv2.cvtColor(frame_disp, cv2.COLOR_RGB2BGR)

            # Draw detections on display frame
            scale_x = frame_disp.shape[1] / img_w
            scale_y = frame_disp.shape[0] / img_h
            for (x1, y1, x2, y2, score) in faces:
                cv2.rectangle(frame_disp,
                              (int(x1 * scale_x), int(y1 * scale_y)),
                              (int(x2 * scale_x), int(y2 * scale_y)),
                              (0, 255, 0), 2)
                cv2.putText(frame_disp, f"{score:.2f}",
                            (int(x1 * scale_x), max(15, int(y1 * scale_y) - 10)),
                            cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 255, 0), 2)

            # FPS calculation
            frame_count += 1
            elapsed = time.time() - prev_time
            if elapsed >= 1.0:
                fps = frame_count / elapsed
                frame_count = 0
                prev_time = time.time()
                cv2.putText(frame_disp, f"FPS: {fps:.2f}", (20, 30),
                            cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 255, 255), 2)

            cv2.imshow('RetinaFace Detection', frame_disp)
            if cv2.waitKey(1) & 0xFF == ord('q'):
                break

    finally:
        picam2.stop()
        cv2.destroyAllWindows()
        logging.info("Exiting.")

if __name__ == "__main__":
    main()
