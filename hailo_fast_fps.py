import os
import time
import argparse
import logging
import degirum as dg
from picamera2 import Picamera2
from libcamera import Transform

logging.basicConfig(level=logging.INFO, format='[%(levelname)s] %(message)s')


class ObjectDetector:
    def __init__(self, model_name: str, zoo_path: str):
        logging.info(f"Connecting to DeGirum in local mode — zoo: {zoo_path}")
        self.zoo = dg.connect("localhost") #dg.connect(dg.LOCAL, zoo_path)
        logging.info(f"Loading model: {model_name}")
        self.model = self.zoo.load_model(model_name)
        logging.info("Model loaded successfully.")

    def infer(self, frame_rgb):
        return self.model.predict(frame_rgb)

    def parse_detections(self, outputs):
        """Return total detections (no filtering)"""
        try:
            return len(outputs.results)
        except Exception:
            return 0


def run_fps_test(width, height, model_name):
    zoo_path = os.path.expanduser("~/degirum-zoo")
    detector = ObjectDetector(model_name, zoo_path)

    # Configure Picamera2 with LoRes stream at exact model size
    picam2 = Picamera2()
    config = picam2.create_video_configuration(
        main={"size": (2028, 1520)},  # HQ cam native binned mode for speed
        lores={"size": (width, height), "format": "RGB888"},
        transform=Transform(vflip=0, hflip=0),
        buffer_count=3
    )
    picam2.configure(config)
    picam2.start()

    logging.info(f"Starting optimized FPS test with LoRes {width}x{height}. Press CTRL+C to stop.")

    # Function handles
    infer = detector.infer
    parse = detector.parse_detections
    get_frame = picam2.capture_array  # Will pull from LoRes stream

    frame_count = 0
    total_detections = 0
    start_time = time.time()

    try:
        while True:
            # Pull from LoRes stream without extra processing
            frame_rgb = picam2.capture_array("lores")
            outputs = infer(frame_rgb)
            num_objects = parse(outputs)

            frame_count += 1
            total_detections += num_objects

            elapsed = time.time() - start_time
            if elapsed >= 1.0:
                fps = frame_count / elapsed
                print(f"\rFPS: {fps:.2f} | Detections/sec: {total_detections}", end="")
                frame_count = 0
                total_detections = 0
                start_time = time.time()

    except KeyboardInterrupt:
        logging.info("\nFPS test stopped.")
    finally:
        picam2.stop()


if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="Optimized HQ Camera + Hailo FPS test")
    parser.add_argument("--width", type=int, default=640, help="Inference width")
    parser.add_argument("--height", type=int, default=640, help="Inference height")
    parser.add_argument("--model", type=str, default="yolov8n_relu6_car--640x640_quant_hailort_multidevice_1",
                        help="Model name from degirum-zoo")
    args = parser.parse_args()

    run_fps_test(args.width, args.height, args.model)
