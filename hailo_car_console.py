import os
import time
import argparse
import logging
from threading import Thread
from queue import Queue

import degirum as dg
from picamera2 import Picamera2

logging.basicConfig(level=logging.INFO, format='[%(levelname)s] %(message)s')


class CarDetector:
    def __init__(self, model_name: str, zoo_path: str):
        logging.info(f"Connecting to DeGirum in local mode — zoo: {zoo_path}")
        self.zoo = dg.connect(dg.LOCAL, zoo_path)
        logging.info(f"Loading model: {model_name}")
        self.model = self.zoo.load_model(model_name)
        logging.info("Model loaded successfully.")

    def infer(self, frame_rgb):
        return self.model.predict(frame_rgb)

    def parse_detections(self, outputs):
        """Count only vehicles (car, truck, bus, motorcycle, etc.)"""
        try:
            count = 0
            for det in outputs.results:
                label = det.label.lower()
                if label in ["car", "truck", "bus", "motorcycle", "van"]:
                    count += 1
            return count
        except Exception:
            return 0


def run_fps_test(width, height):
    zoo_path = os.path.expanduser("~/degirum-zoo")
    model_name = "yolov8n_relu6_car--640x640_quant_hailort_multidevice_1"  # Change if needed

    # Initialize detector
    detector = CarDetector(model_name, zoo_path)

    # Setup Picamera2 to capture directly in RGB888 (no color conversion)
    picam2 = Picamera2()
    preview_cfg = picam2.create_preview_configuration(
        main={"format": "RGB888", "size": (width, height)}
    )
    picam2.configure(preview_cfg)
    picam2.start()

    # Queue for frames
    frame_queue = Queue(maxsize=2)

    # Function to continuously capture frames in a separate thread
    def capture_frames():
        capture = picam2.capture_array
        while True:
            try:
                frame_rgb = capture()
                frame_queue.put(frame_rgb, timeout=0.5)
            except Exception as e:
                logging.error(f"Capture error: {e}")

    # Start capture thread
    Thread(target=capture_frames, daemon=True).start()

    logging.info(f"Starting car detection FPS test at resolution {width}x{height}. Press CTRL+C to stop.")

    # Local function references for speed
    infer = detector.infer
    parse = detector.parse_detections

    frame_count = 0
    total_detections = 0
    start_time = time.time()

    try:
        while True:
            frame_rgb = frame_queue.get()
            outputs = infer(frame_rgb)
            num_cars = parse(outputs)

            frame_count += 1
            total_detections += num_cars

            elapsed = time.time() - start_time
            if elapsed >= 1.0:
                fps = frame_count / elapsed
                print(f"\rFPS: {fps:.2f} | Cars/sec: {total_detections}", end="")
                frame_count = 0
                total_detections = 0
                start_time = time.time()

    except KeyboardInterrupt:
        logging.info("\nFPS test stopped.")
    finally:
        picam2.stop()


if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="Optimized Hailo car detection FPS test")
    parser.add_argument("--width", type=int, default=640, help="Image width (default: 640)")
    parser.add_argument("--height", type=int, default=640, help="Image height (default: 640)")
    args = parser.parse_args()

    run_fps_test(args.width, args.height)
