import chromadb

# Persistent client — stored on disk
client = chromadb.PersistentClient(path="db")

collection = client.create_collection(name="test_collection")

collection.add(
    documents=["This is a test document about Raspberry Pi and Hailo."],
    embeddings=[[0.1, 0.2, 0.3]],
    ids=["doc1"]
)

results = collection.query(
    query_embeddings=[[0.1, 0.2, 0.3]],
    n_results=1
)

print(results)
