#!/usr/bin/env python3
"""
hailo_detect_record_buffered_fixed.py

Fixed version with proper FPS handling and video sizing:
- Accurate frame rate timing using precise timing control
- Automatic video size detection from input source
- Proper frame buffering with consistent timing
- Guaranteed MP4 output with correct FPS and resolution
"""

from __future__ import annotations

import argparse
import logging
import os
import queue
import signal
import subprocess
import threading
import time
from datetime import datetime
from typing import Any, Optional, Tuple

import cv2
import numpy as np

# Try to import optional dependencies
try:
    import degirum as dg
    DEGIRUM_AVAILABLE = True
except ImportError:
    logging.warning("degirum not available - running without AI inference")
    DEGIRUM_AVAILABLE = False

try:
    from picamera2 import Picamera2
    PICAMERA2_AVAILABLE = True
except ImportError:
    logging.warning("picamera2 not available - will use test frames or webcam")
    PICAMERA2_AVAILABLE = False

# --- Logging ---
logging.basicConfig(level=logging.INFO, format="[%(asctime)s] [%(levelname)s] %(message)s",
                    datefmt="%Y-%m-%d %H:%M:%S")


def parse_det_fields(det: Any) -> Tuple[str, float, Optional[Tuple[int, int, int, int]]]:
    """
    Return (label, score, bbox) where bbox is (x1,y1,x2,y2) or None.
    Accepts objects with attributes or dict-like outputs.
    """
    # label
    label = getattr(det, "label", None)
    if label is None:
        try:
            label = det.get("label")
        except Exception:
            label = None
    label = label or "detection"

    # score
    score = getattr(det, "confidence", None)
    if score is None:
        try:
            score = det.get("score", det.get("confidence", 0.0))
        except Exception:
            score = 0.0
    try:
        score = float(score)
    except Exception:
        score = 0.0

    # bbox
    raw_bbox = getattr(det, "bbox", None)
    if raw_bbox is None:
        try:
            raw_bbox = det.get("bbox")
        except Exception:
            raw_bbox = None

    if raw_bbox is None:
        return label, score, None

    try:
        coords = list(map(int, raw_bbox))
        if len(coords) == 4:
            x0, y0, a, b = coords
            # If a,b look like width/height assume w,h else assume x2,y2
            if 0 <= a <= 2000 and 0 <= b <= 2000 and a < 1000 and b < 1000:
                x1, y1, x2, y2 = x0, y0, x0 + a, y0 + b
            else:
                x1, y1, x2, y2 = x0, y0, a, b
            return label, score, (x1, y1, x2, y2)
    except Exception:
        pass

    return label, score, None


class FrameTimer:
    """Precise frame timing controller"""
    
    def __init__(self, target_fps: float):
        self.target_fps = target_fps
        self.frame_interval = 1.0 / target_fps
        self.last_frame_time = time.time()
        self.frame_count = 0
        self.start_time = time.time()
        
    def wait_for_next_frame(self) -> bool:
        """Wait until it's time for the next frame. Returns True if we should continue."""
        current_time = time.time()
        expected_time = self.start_time + (self.frame_count * self.frame_interval)
        
        if current_time < expected_time:
            # We're ahead of schedule, sleep
            sleep_time = expected_time - current_time
            if sleep_time > 0:
                time.sleep(sleep_time)
        
        self.frame_count += 1
        self.last_frame_time = time.time()
        return True
        
    def get_actual_fps(self) -> float:
        """Get the actual FPS achieved so far"""
        elapsed = time.time() - self.start_time
        return self.frame_count / elapsed if elapsed > 0 else 0.0


class SimpleVideoWriter:
    """Bulletproof video writer with codec fallbacks and proper FPS handling"""
    
    def __init__(self, output_path: str, width: int, height: int, fps: float):
        self.output_path = output_path
        self.width = width
        self.height = height
        self.fps = fps
        self.writer = None
        self.frames_written = 0
        self.is_open = False
        
    def start(self) -> bool:
        """Start the video writer with fallback codecs"""
        codecs_to_try = [
            ('mp4v', '.mp4'),  # Try MP4 first 
            ('MJPG', '.avi'),
            ('XVID', '.avi'), 
            ('H264', '.mp4'),
            ('X264', '.mp4')
        ]
        
        for codec_name, extension in codecs_to_try:
            try:
                # Adjust output path for codec
                base_path = os.path.splitext(self.output_path)[0]
                test_path = base_path + extension
                
                logging.info(f"Trying video codec: {codec_name} -> {test_path}")
                
                fourcc = cv2.VideoWriter_fourcc(*codec_name)
                writer = cv2.VideoWriter(test_path, fourcc, self.fps, (self.width, self.height))
                
                if writer.isOpened():
                    self.writer = writer
                    self.output_path = test_path
                    self.is_open = True
                    logging.info(f"Video writer started: {codec_name} at {self.fps} FPS, {self.width}x{self.height}")
                    return True
                else:
                    writer.release()
                    
            except Exception as e:
                logging.warning(f"Failed to start writer with {codec_name}: {e}")
                
        logging.error("Failed to start video writer with any codec!")
        return False
    
    def write_frame(self, frame: np.ndarray) -> bool:
        """Write a single frame"""
        if not self.is_open or self.writer is None:
            return False
            
        try:
            # Ensure frame is correct size
            if frame.shape[:2] != (self.height, self.width):
                frame = cv2.resize(frame, (self.width, self.height))
                
            # Ensure BGR format with 3 channels
            if len(frame.shape) == 3 and frame.shape[2] == 3:
                self.writer.write(frame)
                self.frames_written += 1
                return True
            else:
                logging.warning(f"Invalid frame format: {frame.shape}")
                return False
                
        except Exception as e:
            logging.error(f"Error writing frame: {e}")
            return False
    
    def stop(self):
        """Stop and close the writer"""
        success = False
        if self.writer:
            try:
                self.writer.release()
                self.is_open = False
                
                # Check output file
                if os.path.exists(self.output_path):
                    size = os.path.getsize(self.output_path)
                    logging.info(f"Video saved: {self.output_path} ({size} bytes, {self.frames_written} frames)")
                    success = size > 0
                else:
                    logging.error("Output video file was not created!")
                    
            except Exception as e:
                logging.error(f"Error stopping video writer: {e}")
        
        return success


def video_writer_thread(frame_queue: queue.Queue, writer: SimpleVideoWriter, stop_event: threading.Event):
    """Dedicated thread for writing video frames"""
    logging.info("Video writer thread started")
    
    if not writer.start():
        logging.error("Failed to start video writer")
        return
        
    frames_processed = 0
    last_log_time = time.time()
    
    try:
        while not stop_event.is_set() or not frame_queue.empty():
            try:
                # Get frame with timeout
                frame = frame_queue.get(timeout=1.0)
                
                # Write frame
                if writer.write_frame(frame):
                    frames_processed += 1
                else:
                    logging.warning("Failed to write frame")
                    
                frame_queue.task_done()
                
                # Log progress periodically
                now = time.time()
                if now - last_log_time >= 10.0:
                    queue_size = frame_queue.qsize()
                    logging.info(f"Video writer: {frames_processed} frames written, queue: {queue_size}")
                    last_log_time = now
                    
            except queue.Empty:
                continue
            except Exception as e:
                logging.error(f"Error in video writer thread: {e}")
                break
                
    finally:
        success = writer.stop()
        logging.info(f"Video writer thread finished. Total frames: {frames_processed}, Success: {success}")


def detect_input_properties(camera, cap, args):
    """Detect actual input video properties"""
    actual_width, actual_height, actual_fps = args.width, args.height, args.fps
    
    if cap is not None:
        # Get webcam properties
        try:
            actual_width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
            actual_height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
            reported_fps = cap.get(cv2.CAP_PROP_FPS)
            if reported_fps > 0:
                actual_fps = reported_fps
            logging.info(f"Webcam properties: {actual_width}x{actual_height} @ {actual_fps} FPS")
        except Exception as e:
            logging.warning(f"Could not get webcam properties: {e}")
            
    elif camera is not None:
        # Get Picamera2 properties
        try:
            # Get the actual configured resolution
            config = camera.camera_configuration()
            if config and 'main' in config:
                size = config['main'].get('size', (args.width, args.height))
                actual_width, actual_height = size
                logging.info(f"Picamera2 properties: {actual_width}x{actual_height}")
        except Exception as e:
            logging.warning(f"Could not get Picamera2 properties: {e}")
    
    return actual_width, actual_height, actual_fps


def create_test_frame(width: int, height: int, frame_num: int) -> np.ndarray:
    """Create a test frame when camera is not available"""
    frame = np.zeros((height, width, 3), dtype=np.uint8)
    
    # Background gradient
    for y in range(height):
        for x in range(width):
            frame[y, x] = [
                (x * 255 // width),                    # Red
                (y * 255 // height),                   # Green  
                ((x + y) * 255 // (width + height))    # Blue
            ]
    
    # Add frame counter
    cv2.putText(frame, f"Frame {frame_num:04d}", (50, 100), 
                cv2.FONT_HERSHEY_SIMPLEX, 2, (255, 255, 255), 3)
    
    # Add moving circle (simulated detection)
    center_x = int(width/2 + 100 * np.sin(frame_num * 0.1))
    center_y = int(height/2 + 100 * np.cos(frame_num * 0.1))
    cv2.circle(frame, (center_x, center_y), 30, (0, 255, 0), -1)
    cv2.putText(frame, "TEST DETECTION", (center_x-80, center_y-40), 
                cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 255), 2)
    
    # Add timestamp
    timestamp = time.strftime("%H:%M:%S", time.localtime())
    cv2.putText(frame, timestamp, (50, height - 50), 
                cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 255, 255), 2)
    
    return frame


def convert_to_mp4(input_path: str, mp4_path: str, fps: float) -> bool:
    """Convert video to MP4 format using ffmpeg"""
    if not os.path.exists(input_path):
        logging.error(f"Input file not found: {input_path}")
        return False
        
    if input_path.lower().endswith('.mp4'):
        logging.info("File is already MP4 format")
        if input_path != mp4_path:
            try:
                os.rename(input_path, mp4_path)
                logging.info(f"Renamed to: {mp4_path}")
            except:
                pass
        return True
        
    logging.info(f"Converting {input_path} -> {mp4_path}")
    
    # Try direct copy first (fastest)
    cmd = [
        "ffmpeg", "-y", "-loglevel", "warning",
        "-i", input_path,
        "-c", "copy",
        "-movflags", "faststart",
        mp4_path
    ]
    
    try:
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=60)
        if result.returncode == 0:
            logging.info(f"Successfully converted to MP4: {mp4_path}")
            return True
    except:
        pass
    
    # Fallback: re-encode with explicit FPS
    logging.info("Trying re-encode conversion...")
    cmd_reencode = [
        "ffmpeg", "-y", "-loglevel", "warning",
        "-i", input_path,
        "-c:v", "libx264", "-preset", "fast", "-crf", "23",
        "-r", str(fps),  # Force output frame rate
        "-movflags", "faststart",
        mp4_path
    ]
    
    try:
        result = subprocess.run(cmd_reencode, capture_output=True, text=True, timeout=120)
        if result.returncode == 0:
            logging.info(f"Successfully re-encoded to MP4: {mp4_path}")
            return True
        else:
            logging.error(f"Re-encode failed: {result.stderr}")
            return False
    except Exception as e:
        logging.error(f"Conversion error: {e}")
        return False


def main():
    parser = argparse.ArgumentParser(description="Fixed Hailo Detection with Proper FPS and Video Size")
    parser.add_argument("--width", type=int, default=640, help="Target frame width")
    parser.add_argument("--height", type=int, default=480, help="Target frame height") 
    parser.add_argument("--fps", type=float, default=25.0, help="Target frames per second")
    parser.add_argument("--duration", type=int, default=30, help="Recording duration in seconds (0 = infinite)")
    parser.add_argument("--model", type=str, default="retinaface_mobilenet--736x1280_quant_hailort_hailo8l_1", help="Hailo model name")
    parser.add_argument("--zoo", type=str, default=os.path.expanduser("~/degirum-zoo"), help="Model zoo path")
    parser.add_argument("--outdir", type=str, default="videos", help="Output directory")
    parser.add_argument("--queue-size", type=int, default=100, help="Frame queue size")
    parser.add_argument("--test-mode", action="store_true", help="Use test frames instead of camera")
    parser.add_argument("--webcam", action="store_true", help="Use webcam instead of Picamera2")
    parser.add_argument("--webcam-id", type=int, default=0, help="Webcam device ID")
    parser.add_argument("--debug", action="store_true", help="Enable debug logging")
    parser.add_argument("--auto-size", action="store_true", help="Automatically detect input resolution")
    
    args = parser.parse_args()
    
    if args.debug:
        logging.getLogger().setLevel(logging.DEBUG)
    
    # Setup output paths
    os.makedirs(args.outdir, exist_ok=True)
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    base_path = os.path.join(args.outdir, f"hailo_recording_{timestamp}")
    intermediate_path = base_path + ".avi"  # Will be adjusted by writer
    mp4_path = base_path + ".mp4"
    
    # Initialize AI model if available
    model = None
    if not args.test_mode and DEGIRUM_AVAILABLE:
        try:
            logging.info("Loading Hailo AI model...")
            ctx = dg.connect(dg.LOCAL, args.zoo)
            model = ctx.load_model(args.model)
            model.input_format = "RGB"
            model.overlay = []
            logging.info("AI model loaded successfully")
        except Exception as e:
            logging.warning(f"Failed to load AI model: {e}")
            model = None
    
    # Initialize camera
    camera = None
    cap = None
    
    if not args.test_mode:
        if args.webcam:
            # Use OpenCV webcam
            try:
                cap = cv2.VideoCapture(args.webcam_id)
                if cap.isOpened():
                    # Set desired properties
                    cap.set(cv2.CAP_PROP_FRAME_WIDTH, args.width)
                    cap.set(cv2.CAP_PROP_FRAME_HEIGHT, args.height)
                    cap.set(cv2.CAP_PROP_FPS, args.fps)
                    # Set buffer size to reduce latency
                    cap.set(cv2.CAP_PROP_BUFFERSIZE, 1)
                    logging.info(f"Webcam {args.webcam_id} initialized")
                else:
                    cap = None
                    logging.warning("Failed to open webcam")
            except Exception as e:
                logging.warning(f"Webcam initialization failed: {e}")
                cap = None
        elif PICAMERA2_AVAILABLE:
            # Use Picamera2
            try:
                camera = Picamera2()
                config = camera.create_video_configuration(
                    main={"size": (args.width, args.height), "format": "RGB888"},
                    buffer_count=2  # Reduce buffer to minimize latency
                )
                camera.configure(config)
                camera.start()
                logging.info("Picamera2 initialized")
            except Exception as e:
                logging.warning(f"Picamera2 initialization failed: {e}")
                camera = None
    
    if camera is None and cap is None:
        logging.info("Using test frame mode")
        args.test_mode = True
    
    # Detect actual input properties
    if args.auto_size:
        actual_width, actual_height, actual_fps = detect_input_properties(camera, cap, args)
    else:
        actual_width, actual_height, actual_fps = args.width, args.height, args.fps
    
    logging.info(f"Video output will be: {actual_width}x{actual_height} @ {actual_fps} FPS")
    
    # Create video writer and queue
    frame_queue = queue.Queue(maxsize=args.queue_size)
    video_writer = SimpleVideoWriter(intermediate_path, actual_width, actual_height, actual_fps)
    
    # Setup frame timer for precise timing
    frame_timer = FrameTimer(actual_fps)
    
    # Setup stop event and signal handlers
    stop_event = threading.Event()
    
    def signal_handler(signum, frame):
        logging.info(f"Received signal {signum}, stopping...")
        stop_event.set()
    
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    
    # Start video writer thread
    writer_thread = threading.Thread(
        target=video_writer_thread,
        args=(frame_queue, video_writer, stop_event),
        daemon=True
    )
    writer_thread.start()
    
    # Main capture and inference loop
    logging.info(f"Starting capture {'for ' + str(args.duration) + ' seconds' if args.duration > 0 else '(infinite)'}...")
    start_time = time.time()
    frame_count = 0
    detection_count = 0
    
    try:
        while not stop_event.is_set():
            current_time = time.time()
            if args.duration > 0 and current_time - start_time >= args.duration:
                logging.info("Recording duration reached")
                break
            
            # Wait for proper frame timing
            frame_timer.wait_for_next_frame()
            
            # Capture frame
            frame_bgr = None
            
            if args.test_mode:
                # Generate test frame
                test_frame = create_test_frame(actual_width, actual_height, frame_count)
                frame_bgr = test_frame.copy()
                frame_rgb = cv2.cvtColor(test_frame, cv2.COLOR_BGR2RGB)
            elif cap is not None:
                # Webcam capture
                ret, frame_bgr = cap.read()
                if not ret:
                    logging.warning("Failed to capture from webcam")
                    continue
                
                # Resize to target resolution if needed
                if frame_bgr.shape[:2] != (actual_height, actual_width):
                    frame_bgr = cv2.resize(frame_bgr, (actual_width, actual_height))
                frame_rgb = cv2.cvtColor(frame_bgr, cv2.COLOR_BGR2RGB)
                
            elif camera is not None:
                # Picamera2 capture
                try:
                    frame_rgb = camera.capture_array()
                    # Ensure correct size
                    if frame_rgb.shape[:2] != (actual_height, actual_width):
                        frame_rgb = cv2.resize(frame_rgb, (actual_width, actual_height))
                    frame_bgr = cv2.cvtColor(frame_rgb, cv2.COLOR_RGB2BGR)
                except Exception as e:
                    logging.warning(f"Camera capture failed: {e}")
                    continue
            else:
                logging.error("No capture source available")
                break
            
            # AI Inference
            detections = []
            if model is not None and frame_rgb is not None:
                try:
                    outputs = model(frame_rgb)
                    dets = getattr(outputs, "results", outputs) or []
                    detections = dets
                except Exception as e:
                    logging.debug(f"Inference error: {e}")
            
            # Draw detections on frame
            num_detections = 0
            if detections and frame_bgr is not None:
                try:
                    for det in detections:
                        label, score, bbox = parse_det_fields(det)
                        if bbox and score > 0.5:  # Confidence threshold
                            x1, y1, x2, y2 = bbox
                            # Ensure coordinates are within frame
                            x1 = max(0, min(actual_width - 1, x1))
                            y1 = max(0, min(actual_height - 1, y1))
                            x2 = max(0, min(actual_width - 1, x2))
                            y2 = max(0, min(actual_height - 1, y2))
                            
                            # Draw bounding box
                            cv2.rectangle(frame_bgr, (x1, y1), (x2, y2), (0, 255, 0), 2)
                            cv2.putText(frame_bgr, f"{label} {score:.2f}", 
                                      (x1, max(12, y1 - 6)),
                                      cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 0), 2)
                            num_detections += 1
                except Exception as e:
                    logging.debug(f"Error drawing detections: {e}")
            
            # Add frame to video queue
            if frame_bgr is not None:
                try:
                    frame_queue.put_nowait(frame_bgr.copy())
                    frame_count += 1
                    detection_count += num_detections
                except queue.Full:
                    # Drop oldest frame and add new one
                    try:
                        _ = frame_queue.get_nowait()
                        frame_queue.task_done()
                        frame_queue.put_nowait(frame_bgr.copy())
                        frame_count += 1
                        detection_count += num_detections
                    except:
                        pass
            
            # Progress logging
            if frame_count % (int(actual_fps) * 5) == 0:
                actual_fps_achieved = frame_timer.get_actual_fps()
                det_rate = detection_count / (time.time() - start_time)
                queue_size = frame_queue.qsize()
                logging.info(f"Captured {frame_count} frames, Target FPS: {actual_fps:.1f}, "
                           f"Actual FPS: {actual_fps_achieved:.1f}, Detections/sec: {det_rate:.1f}, Queue: {queue_size}")
    
    except KeyboardInterrupt:
        logging.info("Interrupted by user")
    except Exception as e:
        logging.error(f"Error in capture loop: {e}")
    
    finally:
        # Cleanup
        logging.info("Stopping capture and finalizing video...")
        stop_event.set()
        
        # Stop camera
        if camera:
            try:
                camera.stop()
            except Exception as e:
                logging.warning(f"Error stopping camera: {e}")
        
        if cap:
            try:
                cap.release()
            except Exception as e:
                logging.warning(f"Error releasing webcam: {e}")
        
        # Wait for writer to finish
        writer_thread.join(timeout=30)
        if writer_thread.is_alive():
            logging.warning("Video writer thread didn't finish in time")
        
        # Convert to MP4 with correct FPS
        if os.path.exists(video_writer.output_path):
            final_output = video_writer.output_path
            if not final_output.lower().endswith('.mp4'):
                if convert_to_mp4(video_writer.output_path, mp4_path, actual_fps):
                    final_output = mp4_path
                    # Remove intermediate file
                    try:
                        os.remove(video_writer.output_path)
                        logging.info(f"Removed intermediate file: {video_writer.output_path}")
                    except:
                        pass
            
            # Final stats
            if os.path.exists(final_output):
                size = os.path.getsize(final_output)
                actual_fps_achieved = frame_timer.get_actual_fps()
                logging.info(f"Final video: {final_output} ({size} bytes)")
                logging.info(f"Recording complete! Captured {frame_count} frames at {actual_fps_achieved:.1f} FPS "
                           f"with {detection_count} total detections")
            
        else:
            logging.error("No output file was created!")


if __name__ == "__main__":
    main()
