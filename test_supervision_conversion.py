#!/usr/bin/env python3
"""
Test script to validate <PERSON><PERSON> to Supervision conversion functions.
"""

import numpy as np
import sys
import os

# Add current directory to path to import the main script
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

try:
    import supervision as sv
    print("✓ Supervision library is available")
except ImportError:
    print("✗ Supervision library not available")
    sys.exit(1)

# Import conversion functions from the main script
from jk_face_recong_test import hailo_detections_to_supervision, supervision_detections_to_hailo

def test_conversion_functions():
    """Test the conversion functions with sample data."""
    print("\n=== Testing Hailo to Supervision Conversion ===")
    
    # Create sample Hailo detections
    hailo_detections = [
        {
            "bbox": [100, 100, 200, 200],
            "score": 0.95,
            "emb": np.random.rand(512).astype(np.float32)
        },
        {
            "bbox": [300, 150, 400, 250],
            "score": 0.87,
            "emb": np.random.rand(512).astype(np.float32)
        }
    ]
    
    class_names = ["person1", "person2"]
    
    print(f"Input Hailo detections: {len(hailo_detections)} detections")
    for i, det in enumerate(hailo_detections):
        print(f"  Detection {i+1}: bbox={det['bbox']}, score={det['score']:.2f}, emb_shape={det['emb'].shape}")
    
    # Convert to Supervision format
    sv_detections = hailo_detections_to_supervision(hailo_detections, class_names)
    
    if sv_detections is None:
        print("✗ Conversion to Supervision format failed")
        return False
    
    print(f"\n✓ Converted to Supervision format: {len(sv_detections)} detections")
    print(f"  xyxy shape: {sv_detections.xyxy.shape}")
    print(f"  confidence shape: {sv_detections.confidence.shape}")
    print(f"  class_id shape: {sv_detections.class_id.shape}")
    print(f"  data keys: {list(sv_detections.data.keys())}")
    
    # Verify data integrity
    for i in range(len(sv_detections)):
        print(f"  Detection {i+1}: xyxy={sv_detections.xyxy[i]}, conf={sv_detections.confidence[i]:.2f}, class={sv_detections.data['class_name'][i]}")
    
    # Convert back to Hailo format
    converted_back = supervision_detections_to_hailo(sv_detections)
    
    print(f"\n✓ Converted back to Hailo format: {len(converted_back)} detections")
    
    # Verify round-trip conversion
    success = True
    for i, (original, converted) in enumerate(zip(hailo_detections, converted_back)):
        orig_bbox = original["bbox"]
        conv_bbox = converted["bbox"]
        orig_score = original["score"]
        conv_score = converted["score"]
        
        bbox_match = np.allclose(orig_bbox, conv_bbox, rtol=1e-5)
        score_match = abs(orig_score - conv_score) < 1e-5
        
        print(f"  Detection {i+1}: bbox_match={bbox_match}, score_match={score_match}")
        
        if not (bbox_match and score_match):
            success = False
    
    if success:
        print("✓ Round-trip conversion successful!")
    else:
        print("✗ Round-trip conversion failed!")
    
    return success

def test_empty_detections():
    """Test conversion with empty detections."""
    print("\n=== Testing Empty Detections ===")
    
    # Test empty list
    empty_hailo = []
    sv_empty = hailo_detections_to_supervision(empty_hailo)
    
    if sv_empty is not None and len(sv_empty) == 0:
        print("✓ Empty detections handled correctly")
        return True
    else:
        print("✗ Empty detections not handled correctly")
        return False

def test_tracking_integration():
    """Test integration with ByteTrack tracker."""
    print("\n=== Testing Tracking Integration ===")
    
    try:
        # Create sample detections
        hailo_detections = [
            {"bbox": [100, 100, 200, 200], "score": 0.95},
            {"bbox": [300, 150, 400, 250], "score": 0.87}
        ]
        
        sv_detections = hailo_detections_to_supervision(hailo_detections)
        
        if sv_detections is None:
            print("✗ Failed to create supervision detections")
            return False
        
        # Initialize tracker with correct parameter names
        tracker = sv.ByteTrack(
            track_activation_threshold=0.25,
            lost_track_buffer=30,
            minimum_matching_threshold=0.8,
            frame_rate=30
        )
        
        # Update tracker with detections
        tracked_detections = tracker.update_with_detections(sv_detections)
        
        print(f"✓ Tracking successful: {len(tracked_detections)} tracked detections")
        
        # Check if tracker IDs are assigned
        if hasattr(tracked_detections, 'tracker_id') and tracked_detections.tracker_id is not None:
            print(f"  Tracker IDs: {tracked_detections.tracker_id}")
            print("✓ Tracker IDs assigned successfully")
        else:
            print("✗ Tracker IDs not assigned")
            return False
        
        return True
        
    except Exception as e:
        print(f"✗ Tracking integration failed: {e}")
        return False

def main():
    """Run all tests."""
    print("Testing Supervision Integration for Face Recognition")
    print("=" * 50)
    
    tests = [
        ("Conversion Functions", test_conversion_functions),
        ("Empty Detections", test_empty_detections),
        ("Tracking Integration", test_tracking_integration)
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"✗ {test_name} failed with exception: {e}")
            results.append((test_name, False))
    
    print("\n" + "=" * 50)
    print("TEST RESULTS:")
    
    all_passed = True
    for test_name, result in results:
        status = "✓ PASS" if result else "✗ FAIL"
        print(f"  {test_name}: {status}")
        if not result:
            all_passed = False
    
    if all_passed:
        print("\n🎉 All tests passed! The enhanced system is ready to use.")
        print("\nTo use tracking in your face recognition:")
        print("  python jk_face_recong_test.py --enable_tracking --mode ui")
        print("  python jk_face_recong_test.py --use_supervision --mode console")
    else:
        print("\n❌ Some tests failed. Please check the implementation.")
    
    return 0 if all_passed else 1

if __name__ == "__main__":
    sys.exit(main())
