#!/usr/bin/env python3
"""
retinaface_headless.py

Headless (SSH-friendly) RetinaFace + Hailo inference using Picamera2.
Prints FPS and detections/sec to the console (no GUI).
"""
import os
import time
import argparse
import logging
import degirum as dg
from picamera2 import Picamera2
from libcamera import Transform

logging.basicConfig(level=logging.INFO, format='[%(levelname)s] %(message)s')


class RetinaFaceDetector:
    def __init__(self, model_name: str, zoo_path: str = None):
        logging.info("Connecting to DeGirum (localhost)...")
        # Using "localhost" per typical local DeGirum install; change if needed.
        try:
            self.zoo = dg.connect("localhost")
        except Exception:
            # fallback: try dg.LOCAL with provided zoo_path
            if zoo_path:
                logging.info("dg.connect('localhost') failed — trying dg.LOCAL with zoo_path")
                self.zoo = dg.connect(dg.LOCAL, zoo_path)
            else:
                raise
        logging.info(f"Loading model: {model_name}")
        self.model = self.zoo.load_model(model_name)
        logging.info("Model loaded successfully.")
        self._logged_results_example = False

    def infer(self, frame_rgb):
        """Expecting an RGB numpy array shaped (H, W, 3)."""
        return self.model.predict(frame_rgb)

    def parse_outputs(self, outputs, image_width, image_height):
        """Parse outputs into [(x1,y1,x2,y2,score), ...]. Robust to normalized or [x,y,w,h]."""
        faces = []
        for det in getattr(outputs, "results", []) or []:
            try:
                if not self._logged_results_example:
                    logging.debug("Example detection dict: %s", det)
                    self._logged_results_example = True

                bbox = det.get("bbox", None)
                score = det.get("score", None) or det.get("confidence", None)
                if bbox is None or score is None:
                    continue

                if not (isinstance(bbox, (list, tuple)) and len(bbox) == 4):
                    continue

                x0, y0, x1, y1 = bbox

                # normalized coords?
                if max(x0, y0, x1, y1) <= 1.01:
                    x0 *= image_width
                    x1 *= image_width
                    y0 *= image_height
                    y1 *= image_height

                # detect [x,y,w,h] case and convert
                w_candidate, h_candidate = x1, y1
                if (w_candidate > 0 and h_candidate > 0 and
                        (x0 + w_candidate) <= image_width + 1 and
                        (y0 + h_candidate) <= image_height + 1 and
                        (w_candidate < image_width and h_candidate < image_height) and
                        not (x1 > image_width * 0.9 and y1 > image_height * 0.9)):
                    x1 = x0 + w_candidate
                    y1 = y0 + h_candidate

                # Clip & int
                x0 = max(0, min(image_width - 1, int(round(x0))))
                x1 = max(0, min(image_width - 1, int(round(x1))))
                y0 = max(0, min(image_height - 1, int(round(y0))))
                y1 = max(0, min(image_height - 1, int(round(y1))))

                x_min, x_max = sorted((x0, x1))
                y_min, y_max = sorted((y0, y1))

                faces.append((x_min, y_min, x_max, y_max, float(score)))
            except Exception as e:
                logging.error("Error parsing a detection dict: %s", e)
        return faces


def run_headless(width, height, model_name, zoo_path, print_interval):
    detector = RetinaFaceDetector(model_name, zoo_path)

    picam2 = Picamera2()
    # Configure camera to provide a fast RGB888 stream at the requested size.
    # We put that resolution in both main and lores to avoid needing an HQ stream for display.
    config = picam2.create_video_configuration(
        main={"size": (width, height), "format": "RGB888"},
        lores={"size": (width, height), "format": "RGB888"},
        transform=Transform(vflip=0, hflip=0),
        buffer_count=3
    )
    picam2.configure(config)
    picam2.start()
    logging.info(f"Camera started (lores {width}x{height}). Running headless. Press Ctrl+C to quit.")

    # Micro-optimizations: local handles
    infer = detector.infer
    parse = detector.parse_outputs
    capture_lores = lambda: picam2.capture_array("lores")

    frame_count = 0
    total_detections = 0
    t0 = time.monotonic()

    try:
        while True:
            frame_rgb = capture_lores()  # returns RGB (H, W, 3)
            outputs = infer(frame_rgb)

            img_h, img_w = frame_rgb.shape[:2]
            faces = parse(outputs, img_w, img_h)
            num_faces = len(faces)

            frame_count += 1
            total_detections += num_faces

            elapsed = time.monotonic() - t0
            if elapsed >= print_interval:
                fps = frame_count / elapsed
                dets_per_sec = total_detections / elapsed
                # Write single-line status, carriage-return to overwrite.
                print(
                    f"\rFPS: {fps:6.2f} | Detections/sec: {dets_per_sec:6.2f} | "
                    f"Frames: {frame_count:4d} | Faces(avg/frame): { (total_detections/frame_count):5.2f} ",
                    end="", flush=True
                )
                # reset counters
                frame_count = 0
                total_detections = 0
                t0 = time.monotonic()

    except KeyboardInterrupt:
        print()  # newline after status line
        logging.info("Stopping (KeyboardInterrupt).")
    finally:
        picam2.stop()
        logging.info("Camera stopped. Exiting.")


if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="Headless RetinaFace + Hailo (console mode)")
    parser.add_argument("--width", type=int, default=640, help="Inference width (lores)")
    parser.add_argument("--height", type=int, default=480, help="Inference height (lores)")
    parser.add_argument("--model", type=str,
                        default="retinaface_mobilenet--736x1280_quant_hailort_hailo8l_1",
                        help="Model name from degirum-zoo")
    parser.add_argument("--zoo", type=str, default=os.path.expanduser("~/degirum-zoo"),
                        help="Path to degirum zoo (optional)")
    parser.add_argument("--print-interval", type=float, default=1.0,
                        help="Seconds between console FPS prints")
    args = parser.parse_args()

    run_headless(args.width, args.height, args.model, args.zoo, args.print_interval)
