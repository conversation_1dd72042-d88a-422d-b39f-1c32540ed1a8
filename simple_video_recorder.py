#!/usr/bin/env python3
"""
Simple, reliable video recorder that WIL<PERSON> create a video file.
Uses OpenCV directly with robust error handling.
"""

import argparse
import logging
import os
import queue
import signal
import subprocess
import threading
import time
from datetime import datetime

import cv2
import numpy as np

logging.basicConfig(level=logging.INFO, format="[%(asctime)s] [%(levelname)s] %(message)s")

def create_test_frame(width: int, height: int, frame_num: int) -> np.ndarray:
    """Create a colorful test frame"""
    frame = np.zeros((height, width, 3), dtype=np.uint8)
    
    # Background gradient
    for y in range(height):
        for x in range(width):
            frame[y, x] = [
                (x * 255 // width),                    # Red gradient
                (y * 255 // height),                   # Green gradient  
                ((x + y) * 255 // (width + height))    # Blue gradient
            ]
    
    # Add frame counter
    cv2.putText(frame, f"Frame {frame_num:04d}", (50, 100), 
                cv2.FONT_HERSHEY_SIMPLEX, 2, (255, 255, 255), 3)
    
    # Add moving circle
    center_x = int(width/2 + 100 * np.sin(frame_num * 0.1))
    center_y = int(height/2 + 100 * np.cos(frame_num * 0.1))
    cv2.circle(frame, (center_x, center_y), 30, (255, 255, 0), -1)
    
    # Add timestamp
    timestamp = time.strftime("%H:%M:%S", time.localtime())
    cv2.putText(frame, timestamp, (50, height - 50), 
                cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 255, 255), 2)
    
    return frame


class SimpleVideoWriter:
    """Simple, reliable video writer"""
    
    def __init__(self, output_path: str, width: int, height: int, fps: int):
        self.output_path = output_path
        self.width = width
        self.height = height
        self.fps = fps
        self.writer = None
        self.frames_written = 0
        self.is_open = False
        
    def start(self) -> bool:
        """Start the video writer"""
        # Try different codecs in order of preference
        codecs_to_try = [
            ('MJPG', '.avi'),
            ('XVID', '.avi'),
            ('MP4V', '.mp4'),
            ('H264', '.mp4'),
            ('X264', '.mp4')
        ]
        
        for codec_name, extension in codecs_to_try:
            try:
                # Adjust output path for codec
                base_path = os.path.splitext(self.output_path)[0]
                test_path = base_path + extension
                
                logging.info(f"Trying codec: {codec_name} -> {test_path}")
                
                fourcc = cv2.VideoWriter_fourcc(*codec_name)
                writer = cv2.VideoWriter(test_path, fourcc, float(self.fps), (self.width, self.height))
                
                if writer.isOpened():
                    self.writer = writer
                    self.output_path = test_path
                    self.is_open = True
                    logging.info(f"Video writer started with {codec_name} codec: {test_path}")
                    return True
                else:
                    writer.release()
                    
            except Exception as e:
                logging.warning(f"Failed to start writer with {codec_name}: {e}")
                
        logging.error("Failed to start video writer with any codec!")
        return False
    
    def write_frame(self, frame: np.ndarray) -> bool:
        """Write a frame"""
        if not self.is_open or self.writer is None:
            return False
            
        try:
            # Ensure frame is correct size and format
            if frame.shape != (self.height, self.width, 3):
                frame = cv2.resize(frame, (self.width, self.height))
                
            self.writer.write(frame)
            self.frames_written += 1
            return True
            
        except Exception as e:
            logging.error(f"Error writing frame: {e}")
            return False
    
    def stop(self):
        """Stop and close the writer"""
        if self.writer:
            try:
                self.writer.release()
                self.is_open = False
                
                # Check output file
                if os.path.exists(self.output_path):
                    size = os.path.getsize(self.output_path)
                    logging.info(f"Video saved: {self.output_path} ({size} bytes, {self.frames_written} frames)")
                    return size > 0
                else:
                    logging.error("Output file was not created!")
                    return False
                    
            except Exception as e:
                logging.error(f"Error stopping writer: {e}")
                return False
        return False


def video_writer_thread(frame_queue: queue.Queue, writer: SimpleVideoWriter, stop_event: threading.Event):
    """Thread function for writing video frames"""
    if not writer.start():
        logging.error("Failed to start video writer!")
        return
    
    frames_processed = 0
    last_log = time.time()
    
    try:
        while not stop_event.is_set() or not frame_queue.empty():
            try:
                frame = frame_queue.get(timeout=1.0)
                
                if writer.write_frame(frame):
                    frames_processed += 1
                else:
                    logging.warning("Failed to write frame")
                
                frame_queue.task_done()
                
                # Log progress
                now = time.time()
                if now - last_log >= 5.0:
                    logging.info(f"Writer: {frames_processed} frames written, queue: {frame_queue.qsize()}")
                    last_log = now
                    
            except queue.Empty:
                continue
            except Exception as e:
                logging.error(f"Error in writer thread: {e}")
                break
                
    finally:
        success = writer.stop()
        logging.info(f"Writer thread finished. Frames: {frames_processed}, Success: {success}")


def convert_to_mp4(input_path: str, output_path: str, fps: int) -> bool:
    """Convert video to MP4 format"""
    if not os.path.exists(input_path):
        logging.error(f"Input file not found: {input_path}")
        return False
        
    if input_path.lower().endswith('.mp4'):
        logging.info("File is already MP4 format")
        return True
        
    logging.info(f"Converting {input_path} -> {output_path}")
    
    cmd = [
        "ffmpeg", "-y", "-i", input_path,
        "-c:v", "libx264", "-preset", "medium", "-crf", "23",
        "-movflags", "+faststart",
        output_path
    ]
    
    try:
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=120)
        if result.returncode == 0:
            logging.info(f"Successfully converted to MP4: {output_path}")
            return True
        else:
            logging.error(f"Conversion failed: {result.stderr}")
            return False
    except subprocess.TimeoutExpired:
        logging.error("Conversion timed out")
        return False
    except Exception as e:
        logging.error(f"Conversion error: {e}")
        return False


def main():
    parser = argparse.ArgumentParser(description="Simple, reliable video recorder")
    parser.add_argument("--width", type=int, default=640)
    parser.add_argument("--height", type=int, default=480)
    parser.add_argument("--fps", type=int, default=15)
    parser.add_argument("--duration", type=int, default=10)
    parser.add_argument("--outdir", type=str, default="videos")
    parser.add_argument("--queue-size", type=int, default=50)
    
    args = parser.parse_args()
    
    # Setup
    os.makedirs(args.outdir, exist_ok=True)
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    base_path = os.path.join(args.outdir, f"test_recording_{timestamp}")
    output_path = base_path + ".avi"  # Will be adjusted by writer
    mp4_path = base_path + ".mp4"
    
    # Create components
    frame_queue = queue.Queue(maxsize=args.queue_size)
    video_writer = SimpleVideoWriter(output_path, args.width, args.height, args.fps)
    stop_event = threading.Event()
    
    def signal_handler(signum, frame):
        logging.info(f"Signal {signum} received, stopping...")
        stop_event.set()
    
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    
    # Start writer thread
    writer_thread = threading.Thread(
        target=video_writer_thread,
        args=(frame_queue, video_writer, stop_event),
        daemon=True
    )
    writer_thread.start()
    
    # Main capture loop
    logging.info(f"Starting recording for {args.duration} seconds...")
    start_time = time.time()
    frame_count = 0
    
    try:
        while not stop_event.is_set() and (time.time() - start_time) < args.duration:
            # Create test frame
            frame = create_test_frame(args.width, args.height, frame_count)
            
            # Add to queue
            try:
                frame_queue.put_nowait(frame)
                frame_count += 1
            except queue.Full:
                # Drop oldest frame and add new one
                try:
                    _ = frame_queue.get_nowait()
                    frame_queue.task_done()
                    frame_queue.put_nowait(frame)
                    frame_count += 1
                except:
                    pass
            
            # Control frame rate
            time.sleep(1.0 / args.fps)
            
            # Progress logging
            if frame_count % (args.fps * 5) == 0:
                elapsed = time.time() - start_time
                actual_fps = frame_count / elapsed if elapsed > 0 else 0
                logging.info(f"Captured {frame_count} frames, FPS: {actual_fps:.1f}")
    
    except KeyboardInterrupt:
        logging.info("Interrupted by user")
    except Exception as e:
        logging.error(f"Error in capture loop: {e}")
    
    finally:
        # Cleanup
        stop_event.set()
        logging.info("Stopping capture and waiting for writer...")
        
        writer_thread.join(timeout=30)
        if writer_thread.is_alive():
            logging.warning("Writer thread didn't finish in time")
        
        # Convert to MP4 if needed
        if os.path.exists(video_writer.output_path):
            final_output = video_writer.output_path
            if not final_output.lower().endswith('.mp4'):
                if convert_to_mp4(video_writer.output_path, mp4_path, args.fps):
                    final_output = mp4_path
                    # Optionally remove original
                    try:
                        os.remove(video_writer.output_path)
                        logging.info(f"Removed intermediate file: {video_writer.output_path}")
                    except:
                        pass
            
            logging.info(f"Final output: {final_output}")
            logging.info(f"Total frames captured: {frame_count}")
        else:
            logging.error("No output file was created!")


if __name__ == "__main__":
    main()
