#!/usr/bin/env python3
"""
run_picam_showcase.py

Scenario: Picamera2 or Video file -> model -> draw bounding boxes -> record MP4

Press 'q' in preview window to stop capture.

Usage examples:
  # Use PiCamera2, increase input queue to 32 frames
  python run_picam_showcase.py --input-q-size 32 --save-q-size 256

  # Use a video file and reduce input fps
  python run_picam_showcase.py --video-file /path/to/video.mp4 --input-fps 10 --input-q-size 16
"""

from __future__ import annotations

import argparse
import logging
import time
from typing import Any, Dict, Optional, Tuple

import cv2
import numpy as np

# Import pipeline primitives
from pipeline_simple import (
    Picamera2Source,
    VideoFileSource,
    DegirumRetinaFaceModel,
    DummyModel,
    BufferedPipelineSimple,
)

logger = logging.getLogger("run_showcase")
logging.basicConfig(level=logging.INFO, format="[%(levelname)s] %(message)s")


def safe_parse_bbox(raw_bbox) -> Tuple[int, int, int, int]:
    """
    Parse bbox formats commonly returned by models into (x1, y1, x2, y2) ints.

    Supported input:
      - list/tuple/ndarray of 4 floats/ints: either [x1,y1,x2,y2] or [x,y,w,h]
      - dict with 'bbox' or 'box' key returning the above
    Coordinates may be normalized (0..1) or absolute pixels.
    """
    if raw_bbox is None:
        raise ValueError("bbox is None")

    # If dict passed in, extract likely key
    if isinstance(raw_bbox, dict):
        for k in ("bbox", "box", "boxes"):
            if k in raw_bbox:
                raw_bbox = raw_bbox[k]
                break
        else:
            # fallback to picking numeric fields if present
            if all(k in raw_bbox for k in ("x1", "y1", "x2", "y2")):
                return (int(raw_bbox["x1"]), int(raw_bbox["y1"]), int(raw_bbox["x2"]), int(raw_bbox["y2"]))
            if all(k in raw_bbox for k in ("x", "y", "w", "h")):
                x = float(raw_bbox["x"]); y = float(raw_bbox["y"]); w = float(raw_bbox["w"]); h = float(raw_bbox["h"])
                return (int(round(x)), int(round(y)), int(round(x + w)), int(round(y + h)))
            raise ValueError("Unrecognized bbox dict format")

    arr = np.asarray(raw_bbox, dtype=float).flatten()
    if arr.size != 4:
        raise ValueError("bbox must have exactly 4 values")

    x0, y0, x1, y1 = arr.tolist()

    # Heuristic: if values appear normalized (<=1.0), caller must convert using frame size later.
    return (x0, y0, x1, y1)  # return as floats; caller will round and clamp


def make_process_fn(conf_threshold: float = 0.0):
    """
    Returns a process_fn compatible with BufferedPipelineSimple:
        process_fn(frame_rgb, model_out, pkt) -> processed_rgb (uint8 RGB)

    This implementation:
      - extracts *all* detections from model_out (common DeGirum formats, plus arrays)
      - applies optional confidence threshold
      - draws a bbox + score for every detection
    """
    def _iter_detections(result_item):
        """Yield dicts of the shape {'bbox': [...], 'score': float|None} from many common shapes."""
        if result_item is None:
            return

        # Numpy array cases
        if isinstance(result_item, np.ndarray):
            arr = result_item
            if arr.ndim == 1 and arr.size >= 4:
                # [x1,y1,x2,y2,(score)]
                bbox = arr[:4].tolist()
                score = float(arr[4]) if arr.size >= 5 else None
                yield {"bbox": bbox, "score": score}
                return
            if arr.ndim == 2 and arr.shape[1] in (4, 5):
                for row in arr:
                    bbox = row[:4].tolist()
                    score = float(row[4]) if row.shape[0] >= 5 else None
                    yield {"bbox": bbox, "score": score}
                return

        # List/tuple: could be list of dicts or list of arrays
        if isinstance(result_item, (list, tuple)):
            for x in result_item:
                yield from _iter_detections(x)
            return

        # Dict cases
        if isinstance(result_item, dict):
            # Common keys
            bbox = (
                result_item.get("bbox")
                or result_item.get("box")
                or result_item.get("rect")
                or result_item.get("boundingBox")
            )
            score = (
                result_item.get("score")
                or result_item.get("confidence")
                or result_item.get("prob")
            )
            # Sometimes grouped
            for group_key in ("results", "detections", "objects", "boxes"):
                if isinstance(result_item.get(group_key), (list, tuple, np.ndarray)):
                    yield from _iter_detections(result_item[group_key])
                    bbox = None  # avoid double-yield
            # Fallbacks for x,y,w,h or x1,y1,x2,y2 in dict
            if bbox is None:
                if all(k in result_item for k in ("x1", "y1", "x2", "y2")):
                    bbox = [result_item["x1"], result_item["y1"], result_item["x2"], result_item["y2"]]
                elif all(k in result_item for k in ("x", "y")) and any(k in result_item for k in ("w", "width")) and any(k in result_item for k in ("h", "height")):
                    x = result_item["x"]; y = result_item["y"]
                    w = result_item.get("w", result_item.get("width"))
                    h = result_item.get("h", result_item.get("height"))
                    bbox = [x, y, w, h]

            if bbox is not None:
                yield {"bbox": bbox, "score": score}
            return

        # Unknown single item: try to coerce to array
        try:
            arr = np.asarray(result_item).flatten()
            if arr.size >= 4:
                bbox = arr[:4].tolist()
                score = float(arr[4]) if arr.size >= 5 else None
                yield {"bbox": bbox, "score": score}
        except Exception:
            return

    def _to_abs_xyxy(bbox_raw, w, h):
        """Convert bbox to absolute (x1,y1,x2,y2) ints, with clamping."""
        x0f, y0f, x1f, y1f = safe_parse_bbox(bbox_raw)

        # Treat as normalized if values look like 0..1
        if 0.0 <= min(x0f, y0f, x1f, y1f) and max(x0f, y0f, x1f, y1f) <= 1.05:
            x0 = int(round(x0f * w)); x1 = int(round(x1f * w))
            y0 = int(round(y0f * h)); y1 = int(round(y1f * h))
        else:
            # Heuristic for [x, y, w, h] vs [x1, y1, x2, y2]
            if (x1f < x0f) or (y1f < y0f):
                x0 = int(round(x0f)); y0 = int(round(y0f))
                x1 = int(round(x0f + x1f)); y1 = int(round(y0f + y1f))
            else:
                x0 = int(round(x0f)); y0 = int(round(y0f))
                x1 = int(round(x1f)); y1 = int(round(y1f))

        # Clamp to frame
        x0 = max(0, min(w - 1, x0)); x1 = max(0, min(w - 1, x1))
        y0 = max(0, min(h - 1, y0)); y1 = max(0, min(h - 1, y1))
        return x0, y0, x1, y1

    def process_fn(frame_rgb, model_out, pkt):
        processed = frame_rgb.copy()

        try:
            # Normalize model outputs to a flat list of detections
            if model_out is None:
                detections_src = []
            elif hasattr(model_out, "results"):
                detections_src = model_out.results or []
            elif isinstance(model_out, dict) and any(k in model_out for k in ("results", "detections", "objects", "boxes")):
                # if dict with list under a known key
                key = next(k for k in ("results", "detections", "objects", "boxes") if k in model_out)
                detections_src = model_out[key]
            else:
                detections_src = model_out

            parsed = list(_iter_detections(detections_src))
            if not parsed:
                return processed

            # Optional confidence filter
            if conf_threshold is not None:
                try:
                    parsed = [p for p in parsed if (p.get("score") is None) or (float(p["score"]) >= float(conf_threshold))]
                except Exception:
                    # If score values are messy, keep them all rather than crash
                    pass

            if not parsed:
                return processed

            # Draw all detections
            bgr = cv2.cvtColor(processed, cv2.COLOR_RGB2BGR)
            h, w = processed.shape[:2]
            for det in parsed:
                if det.get("bbox") is None:
                    continue
                x0, y0, x1, y1 = _to_abs_xyxy(det["bbox"], w, h)
                cv2.rectangle(bgr, (x0, y0), (x1, y1), (0, 255, 0), 2)
                sc = det.get("score")
                if sc is not None:
                    try:
                        cv2.putText(bgr, f"{float(sc):.2f}", (x0, max(0, y0 - 6)),
                                    cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 0), 1)
                    except Exception:
                        pass

            processed = cv2.cvtColor(bgr, cv2.COLOR_BGR2RGB)
        except Exception as e:
            logger.debug("process_fn exception: %s", e)
            # On error, return original frame to avoid crashing pipeline
            processed = frame_rgb

        return processed

    return process_fn



def on_stats(stats):
    """
    Stats callback to observe queue sizes and dropped frames in realtime.
    Will be invoked periodically by the pipeline (stats_interval).
    """
    # Make a compact, human readable line
    logger.info(
        "stats: processed=%d dropped=%d cap_q=%d save_q=%d preview_q=%d cap_fps=%.2f proc_fps=%.2f uptime=%.1fs",
        stats.processed_frames,
        stats.dropped_frames,
        stats.capture_queue_size,
        stats.save_queue_size,
        stats.preview_queue_size,
        stats.capture_fps,
        stats.processing_fps,
        stats.uptime_seconds,
    )


def build_args():
    ap = argparse.ArgumentParser(description="Picamera2/Video -> model -> draw bboxes -> mp4")
    ap.add_argument("--model-name", default="retinaface_mobilenet--736x1280_quant_hailort_hailo8l_1")
    ap.add_argument("--zoo-path", default="~/degirum-zoo")
    ap.add_argument("--out", default="output.mp4")
    ap.add_argument("--input-fps", type=float, default=15.0)
    ap.add_argument("--out-fps", type=float, default=None)
    ap.add_argument("--video-file", help="Optional path to video file instead of PiCamera2")
    ap.add_argument("--input-q-size", type=int, default=8, help="Input queue size (frames)")
    ap.add_argument("--save-q-size", type=int, default=128, help="Save queue size (frames)")
    ap.add_argument("--preview-q-size", type=int, default=1, help="Preview queue size (frames)")
    ap.add_argument("--conf", type=float, default=0.0, help="Confidence threshold (not used in basic fn)")
    ap.add_argument("--debug", action="store_true", help="Enable debug logging")
    return ap.parse_args()


def main():
    args = build_args()

    # set logging level as requested
    if args.debug:
        logging.getLogger().setLevel(logging.DEBUG)
        logger.debug("Debug logging enabled")

    # Attempt to instantiate DeGirum model; fall back to DummyModel if unavailable
    try:
        model = DegirumRetinaFaceModel(args.model_name, args.zoo_path)
    except Exception as e:
        logger.warning("Degirum model unavailable: %s, using DummyModel.", e)
        model = DummyModel()

    # Choose source
    if args.video_file:
        src = VideoFileSource(args.video_file)
    else:
        try:
            src = Picamera2Source(size=(1280, 736))
        except Exception as e:
            logger.exception("Picamera2 source failed to initialize: %s", e)
            return

    # Build process function & pipeline
    process_fn = make_process_fn(conf_threshold=args.conf)

    pipeline = BufferedPipelineSimple(
        source=src,
        model=model,
        process_fn=process_fn,
        out_path=args.out,
        input_fps=args.input_fps,
        out_fps=args.out_fps,
        input_q_size=max(1, int(args.input_q_size)),
        save_q_size=max(1, int(args.save_q_size)),
        preview_q_size=max(1, int(args.preview_q_size)),
        stats_callback=on_stats,
        stats_interval=1.0,
    )

    try:
        pipeline.start()
        window = "Preview (press q to stop)"
        cv2.namedWindow(window, cv2.WINDOW_NORMAL)

        while True:
            try:
                frame_rgb = pipeline.preview_q.get(timeout=0.5)
            except Exception:
                frame_rgb = None
            if frame_rgb is not None:
                cv2.imshow(window, cv2.cvtColor(frame_rgb, cv2.COLOR_RGB2BGR))
            # handle quit
            if cv2.waitKey(1) & 0xFF == ord("q"):
                pipeline.stop_capture()
                break
            # slight sleep to avoid busy-loop
            time.sleep(0.005)

        # wait for drain/save to complete
        pipeline.join(timeout=120.0)

    except KeyboardInterrupt:
        pipeline.stop_all()
        pipeline.join(timeout=10.0)
    finally:
        try:
            cv2.destroyAllWindows()
        except Exception:
            pass


if __name__ == "__main__":
    main()
#python run_picam_showcase.py --input-q-size 32 --save-q-size 256 --preview-q-size 1
