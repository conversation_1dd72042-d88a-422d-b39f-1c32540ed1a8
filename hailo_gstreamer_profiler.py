#!/usr/bin/env python3
"""
gstreamer_fallback_profiler.py

Tries robust GStreamer pipelines for libcamera -> appsink (BGR for OpenCV),
runs RetinaFace inference (DeGirum/Hailo) and prints FPS + detections/sec.

If GStreamer pipelines fail, optionally falls back to Picamera2 (if available).

Usage:
    python gstreamer_fallback_profiler.py --width 640 --height 480 --warmup 10
"""

import argparse
import time
import sys
import logging
import os

import cv2
import degirum as dg

logging.basicConfig(level=logging.INFO, format='[%(levelname)s] %(message)s')


class RetinaFaceDetector:
    def __init__(self, model_name: str, zoo_path: str):
        logging.info("Connecting to DeGirum (local) — zoo: %s", zoo_path)
        self.zoo = dg.connect(dg.LOCAL, zoo_path)
        logging.info("Loading model: %s", model_name)
        self.model = self.zoo.load_model(model_name)
        logging.info("Model loaded successfully.")
        self._logged_results_example = False

    def preprocess(self, frame_bgr):
        # Model expects RGB; convert here
        return cv2.cvtColor(frame_bgr, cv2.COLOR_BGR2RGB)

    def infer(self, frame_rgb):
        return self.model.predict(frame_rgb)

    def parse_outputs(self, outputs, image_width, image_height):
        faces = []
        try:
            results_iter = outputs.results
        except Exception:
            return faces

        for det in results_iter:
            try:
                bbox = det.get("bbox", None)
                score = det.get("score", None) or det.get("confidence", None)
                if bbox is None or score is None:
                    continue
                if not (isinstance(bbox, (list, tuple)) and len(bbox) == 4):
                    continue
                x0, y0, x1, y1 = bbox
                if max(x0, y0, x1, y1) <= 1.01:
                    x0 *= image_width; x1 *= image_width
                    y0 *= image_height; y1 *= image_height
                w_candidate = x1; h_candidate = y1
                if (w_candidate > 0 and h_candidate > 0 and
                    (x0 + w_candidate) <= image_width + 1 and
                    (y0 + h_candidate) <= image_height + 1 and
                    (w_candidate < image_width and h_candidate < image_height) and
                    not (x1 > image_width * 0.9 and y1 > image_height * 0.9)):
                    x1 = x0 + w_candidate; y1 = y0 + h_candidate
                x1 = max(0, min(image_width - 1, int(round(x1))))
                x0 = max(0, min(image_width - 1, int(round(x0))))
                y1 = max(0, min(image_height - 1, int(round(y1))))
                y0 = max(0, min(image_height - 1, int(round(y0))))
                x_min, x_max = sorted((x0, x1))
                y_min, y_max = sorted((y0, y1))
                faces.append((x_min, y_min, x_max, y_max, float(score)))
            except Exception as e:
                logging.error("Error parsing a detection dict: %s", e)
        return faces


def make_pipeline(width, height):
    """
    Build a conservative pipeline:
      libcamerasrc -> videoconvert -> video/x-raw format=BGR -> appsink
    We force the last caps to BGR so OpenCV reliably gets BGR frames.
    appsink options: max-buffers=1 drop=true sync=false reduce latencies / blocking
    """
    # Note: keep caps simple — let videoconvert handle format translation.
    pipeline = (
        f"libcamerasrc ! video/x-raw,width={width},height={height} ! "
        f"videoconvert ! video/x-raw,format=(string)BGR ! "
        "appsink name=appsink max-buffers=1 drop=true sync=false"
    )
    return pipeline


def try_open_capture(pipeline):
    logging.info("Trying pipeline:\n  %s", pipeline)
    cap = cv2.VideoCapture(pipeline, cv2.CAP_GSTREAMER)
    if cap.isOpened():
        logging.info("Pipeline opened successfully.")
        return cap
    else:
        logging.warning("Pipeline failed to open.")
        try:
            cap.release()
        except Exception:
            pass
        return None


def main():
    parser = argparse.ArgumentParser(description="Robust GStreamer profiler (tries safe BGR pipeline).")
    parser.add_argument("--width", type=int, default=640)
    parser.add_argument("--height", type=int, default=480)
    parser.add_argument("--warmup", type=int, default=10)
    parser.add_argument("--model", type=str, default="retinaface_mobilenet--736x1280_quant_hailort_hailo8l_1")
    parser.add_argument("--zoo", type=str, default="~/degirum-zoo")
    parser.add_argument("--force-picamera2-fallback", action="store_true",
                        help="If GStreamer fails, attempt Picamera2 fallback (if Picamera2 is installed).")
    args = parser.parse_args()

    width, height = args.width, args.height
    gst_pipeline = make_pipeline(width, height)

    cap = try_open_capture(gst_pipeline)

    # If primary pipeline failed, try a minimal pipeline (no explicit caps)
    if cap is None:
        alt_pipeline = f"libcamerasrc ! videoconvert ! video/x-raw,format=(string)BGR,width={width},height={height} ! appsink max-buffers=1 drop=true sync=false"
        cap = try_open_capture(alt_pipeline)

    if cap is None and args.force_picamera2_fallback:
        # Try Picamera2; many of your earlier scripts used Picamera2 successfully.
        try:
            from picamera2 import Picamera2
            logging.info("Attempting Picamera2 fallback...")
            picam2 = Picamera2()
            cfg = picam2.create_preview_configuration(main={"format": "BGR888", "size": (width, height)})
            picam2.configure(cfg)
            picam2.start()
            # Create a tiny wrapper to mimic VideoCapture.read()
            class PicamWrapper:
                def __init__(self, picam):
                    self.picam = picam
                def read(self):
                    arr = self.picam.capture_array()
                    return True, arr
                def isOpened(self):
                    return True
                def release(self):
                    try:
                        self.picam.stop()
                    except Exception:
                        pass
            cap = PicamWrapper(picam2)
            logging.info("Picamera2 fallback ready.")
        except Exception as e:
            logging.error("Picamera2 fallback failed: %s", e)
            cap = None

    if cap is None:
        logging.error("All capture methods failed. Please run 'gst-inspect-1.0 libcamerasrc' and try a gst-launch test.")
        logging.error("Example gst-launch test:\n  gst-launch-1.0 libcamerasrc ! video/x-raw,width=%d,height=%d ! videoconvert ! autovideosink", width, height)
        sys.exit(1)

    # Initialize detector
    zoo_path = os.path.expanduser(args.zoo)
    detector = RetinaFaceDetector(args.model, zoo_path)

    # Warm-up
    logging.info("Warm-up: capturing %d frames", args.warmup)
    for i in range(args.warmup):
        ret, frame = cap.read()
        if not ret or frame is None:
            logging.warning("Warm-up frame %d read failed, retrying...", i + 1)
            time.sleep(0.05)
            continue
        # frame is BGR (we forced BGR caps), so pass directly to preprocess which will convert to RGB
        detector.infer(detector.preprocess(frame))

    logging.info("Warm-up done. Measuring FPS and detections/sec... (CTRL+C to stop)")

    frames_this_second = 0
    detections_this_second = 0
    total_frames = 0
    total_detections = 0

    second_start = time.perf_counter()
    global_start = second_start

    try:
        while True:
            ret, frame = cap.read()
            if not ret or frame is None:
                logging.warning("Frame read failed, sleeping briefly...")
                time.sleep(0.01)
                continue

            # If frame has 4 channels, drop alpha to get BGR
            if frame.ndim == 3 and frame.shape[2] == 4:
                frame = frame[:, :, :3]

            outputs = detector.infer(detector.preprocess(frame))
            faces = detector.parse_outputs(outputs, width, height)
            num_faces = len(faces)

            frames_this_second += 1
            detections_this_second += num_faces
            total_frames += 1
            total_detections += num_faces

            now = time.perf_counter()
            elapsed = now - second_start
            if elapsed >= 1.0:
                fps = frames_this_second / elapsed
                print(f"\rFPS: {fps:.2f} | Detections/sec: {detections_this_second}", end="", flush=True)
                frames_this_second = 0
                detections_this_second = 0
                second_start = now

    except KeyboardInterrupt:
        print()
        total_time = time.perf_counter() - global_start
        avg_fps = total_frames / total_time if total_time > 0 else 0.0
        avg_detections_per_sec = total_detections / total_time if total_time > 0 else 0.0
        logging.info("Stopped by user.")
        logging.info("Total runtime: %.2f s", total_time)
        logging.info("Total frames processed: %d", total_frames)
        logging.info("Total detections: %d", total_detections)
        logging.info("Average FPS: %.2f", avg_fps)
        logging.info("Average detections/sec: %.2f", avg_detections_per_sec)
    finally:
        # release underlying capture if possible
        try:
            cap.release()
        except Exception:
            pass


if __name__ == "__main__":
    main()
